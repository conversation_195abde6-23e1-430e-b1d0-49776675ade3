2025-06-08 16:30:41.262 WARN org.mybatis.spring.mapper.ClassPathMapperScanner-warn: No MyBatis mapper was found in '[com.benzhitech]' package. Please check your configuration.
2025-06-08 16:30:41.708 WARN org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-refresh: Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\code\benzhi\feeservice\target\classes\mapper\LoginMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\code\benzhi\feeservice\target\classes\mapper\LoginMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.benzhitech.model.LoginDO'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.benzhitech.model.LoginDO
