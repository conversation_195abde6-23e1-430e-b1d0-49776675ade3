2025-08-11 21:02:35.494 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 13500 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:02:35.502 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:02:35.656 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@31e55969: startup date [Mon Aug 11 21:02:35 CST 2025]; root of context hierarchy
2025-08-11 21:02:38.454 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: <PERSON><PERSON> initialized with port(s): 8181 (http)
2025-08-11 21:02:38.471 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:02:38.482 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:02:38.482 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:02:38.490 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-11 21:02:38.671 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:02:38.672 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 3017 ms
2025-08-11 21:02:38.748 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:02:39.217 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-11 21:02:39.463 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-11 21:02:39.556 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.731 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@31e55969: startup date [Mon Aug 11 21:02:35 CST 2025]; root of context hierarchy
2025-08-11 21:02:39.781 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.785 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-11 21:02:39.791 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-11 21:02:39.791 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.793 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-11 21:02:39.794 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-11 21:02:39.795 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:02:39.819 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.819 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.877 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-11 21:02:39.999 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-11 21:02:40.263 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-11 21:02:40.297 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-11 21:02:40.299 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-11 21:02:40.306 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-11 21:02:40.320 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-11 21:02:40.335 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-11 21:02:40.351 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-11 21:02:40.356 INFO com.benzhitech.App-logStarted: Started App in 6.275 seconds (JVM running for 8.225)
