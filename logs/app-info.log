2025-08-26 14:59:47.308 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9392 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 14:59:47.323 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 14:59:47.412 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@f437e96: startup date [Tue Aug 26 14:59:47 CST 2025]; root of context hierarchy
2025-08-26 14:59:49.795 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: <PERSON><PERSON> initialized with port(s): 8181 (http)
2025-08-26 14:59:49.812 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 14:59:49.821 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 14:59:49.821 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 14:59:49.824 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-26 14:59:50.054 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 14:59:50.056 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 2644 ms
2025-08-26 14:59:50.182 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 14:59:50.191 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 14:59:50.192 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 14:59:50.192 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 14:59:50.193 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 14:59:50.823 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-26 14:59:51.114 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-26 14:59:51.219 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 14:59:51.429 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@f437e96: startup date [Tue Aug 26 14:59:47 CST 2025]; root of context hierarchy
2025-08-26 14:59:51.490 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-26 14:59:51.491 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 14:59:51.491 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 14:59:51.491 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 14:59:51.493 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-26 14:59:51.495 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-26 14:59:51.495 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-26 14:59:51.496 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-26 14:59:51.496 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-26 14:59:51.496 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.497 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 14:59:51.497 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-26 14:59:51.498 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-26 14:59:51.498 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-26 14:59:51.499 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-26 14:59:51.499 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.499 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-26 14:59:51.500 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.500 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-26 14:59:51.500 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-26 14:59:51.500 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-26 14:59:51.503 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.504 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.504 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-26 14:59:51.505 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 14:59:51.508 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-26 14:59:51.508 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 14:59:51.537 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 14:59:51.537 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 14:59:51.621 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-26 14:59:51.783 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-26 14:59:52.068 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-26 14:59:52.108 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-26 14:59:52.110 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-26 14:59:52.116 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-26 14:59:52.128 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-26 14:59:52.140 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-26 14:59:52.154 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-26 14:59:52.158 INFO com.benzhitech.App-logStarted: Started App in 5.838 seconds (JVM running for 7.513)
2025-08-26 15:01:52.644 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-26 15:01:52.645 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-26 15:01:52.690 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 44 ms
2025-08-26 15:01:53.054 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-26 15:01:53.518 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-26 15:01:53.626 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-26 15:28:10.605 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@f437e96: startup date [Tue Aug 26 14:59:47 CST 2025]; root of context hierarchy
2025-08-26 15:28:10.614 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-26 15:28:10.615 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-26 15:28:10.617 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-26 15:28:10.625 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-26 15:28:31.874 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 19760 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 15:28:31.877 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 15:28:31.987 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7ba9d770: startup date [Tue Aug 26 15:28:31 CST 2025]; root of context hierarchy
2025-08-26 15:28:34.030 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 15:28:34.042 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 15:28:34.050 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 15:28:34.051 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 15:28:34.055 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-26 15:28:34.203 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 15:28:34.203 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 2218 ms
2025-08-26 15:28:34.277 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 15:28:34.284 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 15:28:34.285 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 15:28:34.285 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 15:28:34.285 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 15:28:34.745 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-26 15:28:34.995 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-26 15:28:35.097 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:28:35.298 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7ba9d770: startup date [Tue Aug 26 15:28:31 CST 2025]; root of context hierarchy
2025-08-26 15:28:35.352 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-26 15:28:35.354 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:28:35.354 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:28:35.354 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:28:35.355 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-26 15:28:35.357 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-26 15:28:35.357 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 15:28:35.358 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-26 15:28:35.358 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-26 15:28:35.358 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.359 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-26 15:28:35.360 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-26 15:28:35.362 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-26 15:28:35.362 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-26 15:28:35.363 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-26 15:28:35.363 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.364 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-26 15:28:35.364 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-26 15:28:35.364 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-26 15:28:35.364 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-26 15:28:35.364 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.366 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.367 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-26 15:28:35.367 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.368 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:28:35.370 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-26 15:28:35.371 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 15:28:35.401 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:28:35.401 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:28:35.471 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-26 15:28:35.610 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-26 15:28:35.914 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-26 15:28:35.949 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-26 15:28:35.950 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-26 15:28:35.958 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-26 15:28:35.971 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-26 15:28:35.983 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-26 15:28:36.000 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-26 15:28:36.004 INFO com.benzhitech.App-logStarted: Started App in 4.922 seconds (JVM running for 6.8)
2025-08-26 15:29:19.370 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-26 15:29:19.370 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-26 15:29:19.401 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 31 ms
2025-08-26 15:29:19.669 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-26 15:29:19.958 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-26 15:29:20.024 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-26 15:33:05.408 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7ba9d770: startup date [Tue Aug 26 15:28:31 CST 2025]; root of context hierarchy
2025-08-26 15:33:05.414 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-26 15:33:05.418 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-26 15:33:05.421 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-26 15:33:05.429 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-26 15:33:18.479 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9552 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 15:33:18.482 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 15:33:18.553 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d36ec34: startup date [Tue Aug 26 15:33:18 CST 2025]; root of context hierarchy
2025-08-26 15:33:20.577 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 15:33:20.589 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 15:33:20.599 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 15:33:20.599 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 15:33:20.603 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-26 15:33:20.766 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 15:33:20.766 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 2213 ms
2025-08-26 15:33:20.842 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 15:33:20.846 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 15:33:20.847 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 15:33:20.847 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 15:33:20.847 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 15:33:21.271 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-26 15:33:21.512 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-26 15:33:21.615 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:33:21.813 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d36ec34: startup date [Tue Aug 26 15:33:18 CST 2025]; root of context hierarchy
2025-08-26 15:33:21.864 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-26 15:33:21.866 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:33:21.866 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:33:21.866 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-26 15:33:21.866 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 15:33:21.871 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-26 15:33:21.871 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-26 15:33:21.872 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-26 15:33:21.872 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 15:33:21.872 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.873 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-26 15:33:21.873 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-26 15:33:21.874 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-26 15:33:21.874 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-26 15:33:21.874 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-26 15:33:21.875 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.875 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-26 15:33:21.875 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-26 15:33:21.876 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.876 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-26 15:33:21.876 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-26 15:33:21.877 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.877 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.878 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 15:33:21.878 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-26 15:33:21.880 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-26 15:33:21.881 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 15:33:21.910 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:33:21.911 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 15:33:21.977 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-26 15:33:22.109 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-26 15:33:22.377 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-26 15:33:22.410 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-26 15:33:22.411 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-26 15:33:22.419 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-26 15:33:22.430 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-26 15:33:22.442 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-26 15:33:22.458 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-26 15:33:22.462 INFO com.benzhitech.App-logStarted: Started App in 4.799 seconds (JVM running for 6.351)
2025-08-26 15:33:31.978 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-26 15:33:31.979 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-26 15:33:32.009 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 30 ms
2025-08-26 15:33:32.210 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-26 15:33:32.528 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-26 15:33:32.607 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-26 16:05:41.216 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d36ec34: startup date [Tue Aug 26 15:33:18 CST 2025]; root of context hierarchy
2025-08-26 16:05:41.220 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-26 16:05:41.221 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-26 16:05:41.222 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-26 16:05:41.227 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-26 16:05:41.783 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9552 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 16:05:41.784 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 16:05:41.788 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5dfb3ea: startup date [Tue Aug 26 16:05:41 CST 2025]; root of context hierarchy
2025-08-26 16:05:42.888 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 16:05:42.889 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 16:05:42.890 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 16:05:42.890 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 16:05:42.943 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 16:05:42.944 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1156 ms
2025-08-26 16:05:42.964 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 16:05:42.965 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 16:05:42.965 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 16:05:42.966 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 16:05:42.966 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 16:05:42.989 INFO org.apache.catalina.core.StandardService-log: Stopping service [Tomcat]
2025-08-26 16:05:43.002 INFO org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener-logAutoConfigurationReport: 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-26 16:05:45.263 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9552 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 16:05:45.263 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 16:05:45.266 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@36851ddd: startup date [Tue Aug 26 16:05:45 CST 2025]; root of context hierarchy
2025-08-26 16:05:46.415 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 16:05:46.417 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 16:05:46.417 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 16:05:46.417 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 16:05:46.462 INFO org.apache.catalina.core.ContainerBase.[Tomcat-1].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 16:05:46.464 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1198 ms
2025-08-26 16:05:46.489 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 16:05:46.490 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 16:05:46.490 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 16:05:46.490 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 16:05:46.491 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 16:05:46.652 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-26 16:05:47.235 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-26 16:05:47.295 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:05:47.405 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@36851ddd: startup date [Tue Aug 26 16:05:45 CST 2025]; root of context hierarchy
2025-08-26 16:05:47.422 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-26 16:05:47.423 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 16:05:47.423 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 16:05:47.423 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-26 16:05:47.424 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-26 16:05:47.425 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-26 16:05:47.425 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-26 16:05:47.425 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-26 16:05:47.426 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 16:05:47.426 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.426 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-26 16:05:47.427 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-26 16:05:47.428 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-26 16:05:47.428 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-26 16:05:47.429 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-26 16:05:47.429 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.429 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-26 16:05:47.430 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-26 16:05:47.430 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.431 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-26 16:05:47.431 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-26 16:05:47.432 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.432 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.433 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-26 16:05:47.433 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-26 16:05:47.435 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-26 16:05:47.436 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 16:05:47.451 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:05:47.452 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:05:47.485 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-26 16:05:47.505 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-26 16:05:47.619 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-26 16:05:47.648 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-26 16:05:47.649 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-26 16:05:47.654 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-26 16:05:47.662 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-26 16:05:47.664 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-26 16:05:47.670 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-26 16:05:47.670 INFO com.benzhitech.App-logStarted: Started App in 2.496 seconds (JVM running for 1951.56)
2025-08-26 16:05:47.675 INFO org.springframework.boot.devtools.autoconfigure.ConditionEvaluationDeltaLoggingListener-onApplicationEvent: Condition evaluation unchanged
2025-08-26 16:06:23.589 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@36851ddd: startup date [Tue Aug 26 16:05:45 CST 2025]; root of context hierarchy
2025-08-26 16:06:23.590 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-26 16:06:23.592 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-26 16:06:24.241 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9552 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 16:06:24.242 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 16:06:24.246 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5f4ffcf2: startup date [Tue Aug 26 16:06:24 CST 2025]; root of context hierarchy
2025-08-26 16:06:25.163 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 16:06:25.166 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 16:06:25.166 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 16:06:25.166 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 16:06:25.213 INFO org.apache.catalina.core.ContainerBase.[Tomcat-1].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 16:06:25.214 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 968 ms
2025-08-26 16:06:25.227 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 16:06:25.229 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 16:06:25.229 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 16:06:25.229 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 16:06:25.230 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 16:06:25.261 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:06:25.311 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5f4ffcf2: startup date [Tue Aug 26 16:06:24 CST 2025]; root of context hierarchy
2025-08-26 16:06:25.323 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-26 16:06:25.324 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-26 16:06:25.334 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:06:25.334 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-26 16:06:25.372 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-26 16:06:25.456 INFO org.apache.catalina.core.StandardService-log: Stopping service [Tomcat]
2025-08-26 16:06:25.466 INFO org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener-logAutoConfigurationReport: 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-26 16:06:34.233 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 9552 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-26 16:06:34.234 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-26 16:06:34.239 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2302b0: startup date [Tue Aug 26 16:06:34 CST 2025]; root of context hierarchy
2025-08-26 16:06:35.632 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-26 16:06:35.633 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-26 16:06:35.634 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-26 16:06:35.634 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-26 16:06:35.690 INFO org.apache.catalina.core.ContainerBase.[Tomcat-2].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-26 16:06:35.691 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1452 ms
2025-08-26 16:06:35.706 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-26 16:06:35.708 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-26 16:06:35.709 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-26 16:06:35.709 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-26 16:06:35.710 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-26 16:06:35.731 INFO org.apache.catalina.core.StandardService-log: Stopping service [Tomcat]
2025-08-26 16:06:35.746 INFO org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener-logAutoConfigurationReport: 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
