2025-06-08 16:30:32.868 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 15956 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-06-08 16:30:32.872 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-06-08 16:30:32.931 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1963e16e: startup date [Sun Jun 08 16:30:32 CST 2025]; root of context hierarchy
2025-06-08 16:30:34.401 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: <PERSON><PERSON> initialized with port(s): 8181 (http)
2025-06-08 16:30:34.411 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-06-08 16:30:34.417 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-06-08 16:30:34.418 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-06-08 16:30:34.420 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-06-08 16:30:34.525 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-06-08 16:30:34.525 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1594 ms
2025-06-08 16:30:34.571 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-06-08 16:30:34.574 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-06-08 16:30:34.574 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-06-08 16:30:34.574 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-06-08 16:30:34.574 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-06-08 16:30:34.866 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-06-08 16:30:35.034 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-06-08 16:30:35.096 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:35.215 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1963e16e: startup date [Sun Jun 08 16:30:32 CST 2025]; root of context hierarchy
2025-06-08 16:30:35.249 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-06-08 16:30:35.250 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:35.250 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:35.250 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:35.251 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-06-08 16:30:35.252 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.253 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 16:30:35.253 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-06-08 16:30:35.253 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-06-08 16:30:35.253 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-06-08 16:30:35.254 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-06-08 16:30:35.254 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-06-08 16:30:35.255 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-06-08 16:30:35.255 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-06-08 16:30:35.255 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-06-08 16:30:35.257 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.257 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-06-08 16:30:35.257 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-06-08 16:30:35.257 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-06-08 16:30:35.257 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.258 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-06-08 16:30:35.258 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.258 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.259 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-06-08 16:30:35.259 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:35.260 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-08 16:30:35.260 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 16:30:35.278 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:35.278 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:35.325 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-06-08 16:30:35.401 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-06-08 16:30:35.538 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-06-08 16:30:35.559 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-06-08 16:30:35.560 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-06-08 16:30:35.565 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-06-08 16:30:35.573 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-06-08 16:30:35.583 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-06-08 16:30:35.592 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-06-08 16:30:35.597 INFO com.benzhitech.App-logStarted: Started App in 3.514 seconds (JVM running for 4.683)
2025-06-08 16:30:40.440 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1963e16e: startup date [Sun Jun 08 16:30:32 CST 2025]; root of context hierarchy
2025-06-08 16:30:40.443 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-06-08 16:30:40.443 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-06-08 16:30:40.897 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 15956 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-06-08 16:30:40.898 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-06-08 16:30:40.901 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@39917cf5: startup date [Sun Jun 08 16:30:40 CST 2025]; root of context hierarchy
2025-06-08 16:30:41.476 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-06-08 16:30:41.478 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-06-08 16:30:41.478 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-06-08 16:30:41.479 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-06-08 16:30:41.509 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-06-08 16:30:41.510 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 609 ms
2025-06-08 16:30:41.527 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-06-08 16:30:41.528 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-06-08 16:30:41.528 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-06-08 16:30:41.528 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-06-08 16:30:41.528 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-06-08 16:30:41.557 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:41.604 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@39917cf5: startup date [Sun Jun 08 16:30:40 CST 2025]; root of context hierarchy
2025-06-08 16:30:41.615 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-08 16:30:41.615 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 16:30:41.627 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:41.627 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:41.653 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-06-08 16:30:41.710 INFO org.apache.catalina.core.StandardService-log: Stopping service [Tomcat]
2025-06-08 16:30:41.720 INFO org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener-logAutoConfigurationReport: 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-08 16:30:46.953 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 15956 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-06-08 16:30:46.953 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-06-08 16:30:46.953 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7c34fbae: startup date [Sun Jun 08 16:30:46 CST 2025]; root of context hierarchy
2025-06-08 16:30:47.465 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-06-08 16:30:47.466 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-06-08 16:30:47.466 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-06-08 16:30:47.466 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-06-08 16:30:47.492 INFO org.apache.catalina.core.ContainerBase.[Tomcat-1].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-06-08 16:30:47.492 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 539 ms
2025-06-08 16:30:47.507 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-06-08 16:30:47.507 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-06-08 16:30:47.507 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-06-08 16:30:47.507 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-06-08 16:30:47.507 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-06-08 16:30:47.610 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-06-08 16:30:47.686 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-06-08 16:30:47.721 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:47.759 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7c34fbae: startup date [Sun Jun 08 16:30:46 CST 2025]; root of context hierarchy
2025-06-08 16:30:47.768 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-06-08 16:30:47.770 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:47.770 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:47.770 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 16:30:47.770 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-06-08 16:30:47.770 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-06-08 16:30:47.772 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-06-08 16:30:47.774 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-06-08 16:30:47.774 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-06-08 16:30:47.774 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-06-08 16:30:47.774 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.774 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.776 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.778 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-06-08 16:30:47.778 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 16:30:47.780 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-08 16:30:47.780 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 16:30:47.788 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:47.788 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 16:30:47.810 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-06-08 16:30:47.813 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-06-08 16:30:47.853 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-06-08 16:30:47.858 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-06-08 16:30:47.858 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-06-08 16:30:47.873 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-06-08 16:30:47.873 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-06-08 16:30:47.873 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-06-08 16:30:47.882 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-06-08 16:30:47.882 INFO com.benzhitech.App-logStarted: Started App in 0.971 seconds (JVM running for 16.973)
2025-06-08 16:30:47.882 INFO org.springframework.boot.devtools.autoconfigure.ConditionEvaluationDeltaLoggingListener-onApplicationEvent: Condition evaluation unchanged
2025-06-08 16:40:20.844 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@7c34fbae: startup date [Sun Jun 08 16:30:46 CST 2025]; root of context hierarchy
2025-06-08 16:40:20.847 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-06-08 16:40:20.847 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-06-08 17:34:54.914 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 2656 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-06-08 17:34:54.915 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-06-08 17:34:54.980 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@42f0c523: startup date [Sun Jun 08 17:34:54 CST 2025]; root of context hierarchy
2025-06-08 17:34:56.000 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-06-08 17:34:56.009 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-06-08 17:34:56.014 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-06-08 17:34:56.015 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-06-08 17:34:56.017 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-06-08 17:34:56.103 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-06-08 17:34:56.103 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1123 ms
2025-06-08 17:34:56.143 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-06-08 17:34:56.145 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-06-08 17:34:56.145 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-06-08 17:34:56.145 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-06-08 17:34:56.147 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-06-08 17:34:56.667 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-06-08 17:34:57.177 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-06-08 17:34:57.386 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 17:34:57.829 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@42f0c523: startup date [Sun Jun 08 17:34:54 CST 2025]; root of context hierarchy
2025-06-08 17:34:57.906 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-06-08 17:34:57.909 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 17:34:57.910 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 17:34:57.911 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-06-08 17:34:57.911 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 17:34:57.916 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.917 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-06-08 17:34:57.918 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-06-08 17:34:57.919 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 17:34:57.920 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-06-08 17:34:57.920 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-06-08 17:34:57.921 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-06-08 17:34:57.923 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-06-08 17:34:57.923 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-06-08 17:34:57.923 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-06-08 17:34:57.924 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-06-08 17:34:57.924 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-06-08 17:34:57.925 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-06-08 17:34:57.925 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.925 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-06-08 17:34:57.926 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.927 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.927 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.928 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 17:34:57.930 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-06-08 17:34:57.934 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-08 17:34:57.935 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 17:34:57.972 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 17:34:57.973 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 17:34:58.061 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-06-08 17:34:58.189 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-06-08 17:34:58.407 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-06-08 17:34:58.442 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-06-08 17:34:58.444 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-06-08 17:34:58.449 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-06-08 17:34:58.461 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-06-08 17:34:58.474 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-06-08 17:34:58.488 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-06-08 17:34:58.492 INFO com.benzhitech.App-logStarted: Started App in 3.953 seconds (JVM running for 4.777)
2025-06-08 17:35:06.127 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 17:35:06.128 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-06-08 17:35:06.139 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 11 ms
2025-06-08 17:35:06.255 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-06-08 17:35:06.519 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-06-08 17:35:06.586 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-06-08 17:58:47.292 INFO org.springframework.beans.factory.xml.XmlBeanDefinitionReader-loadBeanDefinitions: Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-06-08 18:02:28.632 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@42f0c523: startup date [Sun Jun 08 17:34:54 CST 2025]; root of context hierarchy
2025-06-08 18:02:28.636 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-06-08 18:02:28.636 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-06-08 18:02:28.637 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-06-08 18:02:28.639 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-06-08 18:02:32.206 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 18968 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-06-08 18:02:32.207 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-06-08 18:02:32.241 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3fa7ea5: startup date [Sun Jun 08 18:02:32 CST 2025]; root of context hierarchy
2025-06-08 18:02:33.288 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-06-08 18:02:33.296 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-06-08 18:02:33.302 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-06-08 18:02:33.302 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-06-08 18:02:33.304 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-06-08 18:02:33.388 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-06-08 18:02:33.388 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1147 ms
2025-06-08 18:02:33.426 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-06-08 18:02:33.429 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-06-08 18:02:33.429 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-06-08 18:02:33.429 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-06-08 18:02:33.429 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-06-08 18:02:33.671 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-06-08 18:02:33.801 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-06-08 18:02:33.860 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 18:02:33.968 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3fa7ea5: startup date [Sun Jun 08 18:02:32 CST 2025]; root of context hierarchy
2025-06-08 18:02:33.999 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-06-08 18:02:34.000 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 18:02:34.000 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 18:02:34.000 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-06-08 18:02:34.001 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-06-08 18:02:34.002 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-06-08 18:02:34.002 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.003 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 18:02:34.003 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-06-08 18:02:34.003 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-06-08 18:02:34.003 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-06-08 18:02:34.003 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-06-08 18:02:34.004 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-06-08 18:02:34.004 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-06-08 18:02:34.004 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-06-08 18:02:34.004 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.005 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.005 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-06-08 18:02:34.005 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-06-08 18:02:34.005 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-06-08 18:02:34.005 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-06-08 18:02:34.007 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.007 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.007 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-06-08 18:02:34.007 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-06-08 18:02:34.009 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-08 18:02:34.009 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-08 18:02:34.023 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 18:02:34.024 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-08 18:02:34.063 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-06-08 18:02:34.126 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-06-08 18:02:34.265 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-06-08 18:02:34.286 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-06-08 18:02:34.287 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-06-08 18:02:34.291 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-06-08 18:02:34.298 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-06-08 18:02:34.306 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-06-08 18:02:34.316 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-06-08 18:02:34.320 INFO com.benzhitech.App-logStarted: Started App in 2.506 seconds (JVM running for 3.25)
2025-06-08 18:02:43.137 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-06-08 18:02:43.137 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-06-08 18:02:43.152 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 14 ms
2025-06-08 18:02:43.252 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-06-08 18:02:43.468 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-06-08 18:02:43.519 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-06-08 18:02:43.602 INFO org.springframework.beans.factory.xml.XmlBeanDefinitionReader-loadBeanDefinitions: Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-06-08 19:25:08.082 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3fa7ea5: startup date [Sun Jun 08 18:02:32 CST 2025]; root of context hierarchy
2025-06-08 19:25:08.087 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-06-08 19:25:08.087 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-06-08 19:25:08.089 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-06-08 19:25:08.091 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
