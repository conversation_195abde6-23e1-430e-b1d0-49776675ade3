2025-08-11 21:02:35.494 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 13500 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:02:35.502 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:02:35.656 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@31e55969: startup date [Mon Aug 11 21:02:35 CST 2025]; root of context hierarchy
2025-08-11 21:02:38.454 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: <PERSON><PERSON> initialized with port(s): 8181 (http)
2025-08-11 21:02:38.471 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:02:38.482 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:02:38.482 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:02:38.490 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-11 21:02:38.671 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:02:38.672 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 3017 ms
2025-08-11 21:02:38.748 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:02:38.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:02:39.217 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-11 21:02:39.463 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-11 21:02:39.556 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.731 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@31e55969: startup date [Mon Aug 11 21:02:35 CST 2025]; root of context hierarchy
2025-08-11 21:02:39.781 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-11 21:02:39.783 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:02:39.785 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-11 21:02:39.786 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-11 21:02:39.787 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-11 21:02:39.789 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-11 21:02:39.790 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-11 21:02:39.791 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-11 21:02:39.791 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.792 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:02:39.793 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-11 21:02:39.794 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-11 21:02:39.795 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:02:39.819 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.819 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:02:39.877 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-11 21:02:39.999 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-11 21:02:40.263 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-11 21:02:40.297 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-11 21:02:40.299 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-11 21:02:40.306 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-11 21:02:40.320 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-11 21:02:40.335 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-11 21:02:40.351 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-11 21:02:40.356 INFO com.benzhitech.App-logStarted: Started App in 6.275 seconds (JVM running for 8.225)
2025-08-11 21:02:54.319 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 21:02:54.320 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-11 21:02:54.347 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 27 ms
2025-08-11 21:02:55.101 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-11 21:02:55.390 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-11 21:02:55.565 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-11 21:25:55.858 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@31e55969: startup date [Mon Aug 11 21:02:35 CST 2025]; root of context hierarchy
2025-08-11 21:25:55.862 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-11 21:25:55.862 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-11 21:25:55.863 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-11 21:25:55.866 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-11 21:26:01.361 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 12084 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:26:01.363 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:26:01.420 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b695595: startup date [Mon Aug 11 21:26:01 CST 2025]; root of context hierarchy
2025-08-11 21:26:03.236 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-11 21:26:03.248 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:26:03.259 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:26:03.259 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:26:03.263 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-11 21:26:03.435 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:26:03.435 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 2015 ms
2025-08-11 21:26:03.514 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:26:03.520 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:26:03.520 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:26:03.521 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:26:03.521 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:26:03.956 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-11 21:26:04.200 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-11 21:26:04.311 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:26:04.493 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b695595: startup date [Mon Aug 11 21:26:01 CST 2025]; root of context hierarchy
2025-08-11 21:26:04.545 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-11 21:26:04.547 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:26:04.547 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:26:04.547 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-11 21:26:04.548 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:26:04.550 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.550 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-11 21:26:04.550 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-11 21:26:04.551 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-11 21:26:04.551 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:26:04.551 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-11 21:26:04.552 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-11 21:26:04.553 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-11 21:26:04.553 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-11 21:26:04.554 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-11 21:26:04.554 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-11 21:26:04.554 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-11 21:26:04.555 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-11 21:26:04.555 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.555 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-11 21:26:04.555 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.556 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.556 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.558 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:26:04.558 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-11 21:26:04.560 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-11 21:26:04.561 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:26:04.586 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:26:04.586 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:26:04.648 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-11 21:26:04.775 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-11 21:26:05.008 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-11 21:26:05.037 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-11 21:26:05.038 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-11 21:26:05.043 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-11 21:26:05.053 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-11 21:26:05.064 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-11 21:26:05.077 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-11 21:26:05.081 INFO com.benzhitech.App-logStarted: Started App in 4.465 seconds (JVM running for 5.909)
2025-08-11 21:26:49.595 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 21:26:49.595 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-11 21:26:49.617 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 22 ms
2025-08-11 21:26:49.736 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-11 21:26:50.377 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-11 21:26:50.449 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-11 21:26:55.052 INFO org.springframework.beans.factory.xml.XmlBeanDefinitionReader-loadBeanDefinitions: Loading XML bean definitions from class path resource [org/springframework/jdbc/support/sql-error-codes.xml]
2025-08-11 21:27:21.955 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5b695595: startup date [Mon Aug 11 21:26:01 CST 2025]; root of context hierarchy
2025-08-11 21:27:21.959 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-11 21:27:21.960 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-11 21:27:21.962 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-11 21:27:21.964 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-11 21:29:28.268 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 20300 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:29:28.270 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:29:28.319 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@612121a4: startup date [Mon Aug 11 21:29:28 CST 2025]; root of context hierarchy
2025-08-11 21:29:31.121 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-11 21:29:31.141 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:29:31.157 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:29:31.158 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:29:31.162 INFO org.apache.catalina.core.AprLifecycleListener-log: The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\code\env\jdk8_64\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\code\env\jdk8_64\bin;D:\code\env\apache-maven-3.9.1\bin;D:\code\env\jdk8_64\jre\bin;D:\code\env\nodejs\;C:\Program Files (x86)\cloudflared\;C:\Program Files\dotnet\;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;;D:\Program Files\微信web开发者工具\dll;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\ZeroTier\One\;D:\code\env\Python310\Scripts\;D:\code\env\Python310\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\code\env\anaconda;D:\code\env\anaconda\Library\bin;D:\code\env\anaconda\Scripts;C:\Users\<USER>\AppData\Roaming\npm;D:\code\env\wix311;D:\Program Files\cursor\resources\app\bin;;.]
2025-08-11 21:29:31.395 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:29:31.396 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 3077 ms
2025-08-11 21:29:31.491 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:29:31.496 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:29:31.497 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:29:31.497 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:29:31.497 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:29:32.072 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-11 21:29:32.392 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-11 21:29:32.524 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:29:32.737 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@612121a4: startup date [Mon Aug 11 21:29:28 CST 2025]; root of context hierarchy
2025-08-11 21:29:32.800 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-11 21:29:32.801 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:29:32.801 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:29:32.802 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:29:32.802 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-11 21:29:32.806 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.806 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-11 21:29:32.807 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:29:32.807 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-11 21:29:32.808 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-11 21:29:32.809 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-11 21:29:32.809 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-11 21:29:32.811 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-11 21:29:32.811 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-11 21:29:32.811 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-11 21:29:32.811 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-11 21:29:32.811 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-11 21:29:32.813 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-11 21:29:32.813 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.814 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-11 21:29:32.814 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.815 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.816 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.816 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:29:32.816 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-11 21:29:32.818 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-11 21:29:32.818 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:29:32.850 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:29:32.851 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:29:32.929 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-11 21:29:33.083 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-11 21:29:33.382 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-11 21:29:33.420 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-11 21:29:33.422 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-11 21:29:33.428 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-11 21:29:33.442 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-11 21:29:33.455 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-11 21:29:33.472 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-11 21:29:33.476 INFO com.benzhitech.App-logStarted: Started App in 5.932 seconds (JVM running for 7.23)
2025-08-11 21:29:42.836 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring FrameworkServlet 'dispatcherServlet'
2025-08-11 21:29:42.837 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization started
2025-08-11 21:29:42.864 INFO org.springframework.web.servlet.DispatcherServlet-initServletBean: FrameworkServlet 'dispatcherServlet': initialization completed in 27 ms
2025-08-11 21:29:43.046 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Starting...
2025-08-11 21:29:43.361 INFO com.zaxxer.hikari.pool.PoolBase-getAndSetNetworkTimeout: HikariPool-1 - Driver does not support get/set network timeout for connections. (com.microsoft.sqlserver.jdbc.SQLServerConnection.getNetworkTimeout()I)
2025-08-11 21:29:43.439 INFO com.zaxxer.hikari.HikariDataSource-getConnection: HikariPool-1 - Start completed.
2025-08-11 21:38:52.114 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@612121a4: startup date [Mon Aug 11 21:29:28 CST 2025]; root of context hierarchy
2025-08-11 21:38:52.118 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-11 21:38:52.119 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
2025-08-11 21:38:52.120 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown initiated...
2025-08-11 21:38:52.124 INFO com.zaxxer.hikari.HikariDataSource-close: HikariPool-1 - Shutdown completed.
2025-08-11 21:38:52.649 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 20300 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:38:52.650 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:38:52.654 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@32d79fc5: startup date [Mon Aug 11 21:38:52 CST 2025]; root of context hierarchy
2025-08-11 21:38:53.667 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-11 21:38:53.669 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:38:53.670 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:38:53.670 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:38:53.730 INFO org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:38:53.732 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 1078 ms
2025-08-11 21:38:53.751 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:38:53.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:38:53.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:38:53.753 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:38:53.754 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:38:53.782 INFO org.apache.catalina.core.StandardService-log: Stopping service [Tomcat]
2025-08-11 21:38:53.797 INFO org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener-logAutoConfigurationReport: 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-11 21:38:55.539 INFO com.benzhitech.App-logStarting: Starting App on WIN10 with PID 20300 (D:\code\benzhi\feeservice\target\classes started by superme in D:\code\benzhi\feeservice)
2025-08-11 21:38:55.539 INFO com.benzhitech.App-logStartupProfileInfo: No active profile set, falling back to default profiles: default
2025-08-11 21:38:55.543 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-prepareRefresh: Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@651837bf: startup date [Mon Aug 11 21:38:55 CST 2025]; root of context hierarchy
2025-08-11 21:38:56.461 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-initialize: Tomcat initialized with port(s): 8181 (http)
2025-08-11 21:38:56.462 INFO org.apache.coyote.http11.Http11NioProtocol-log: Initializing ProtocolHandler ["http-nio-8181"]
2025-08-11 21:38:56.462 INFO org.apache.catalina.core.StandardService-log: Starting service [Tomcat]
2025-08-11 21:38:56.463 INFO org.apache.catalina.core.StandardEngine-log: Starting Servlet Engine: Apache Tomcat/8.5.34
2025-08-11 21:38:56.503 INFO org.apache.catalina.core.ContainerBase.[Tomcat-1].[localhost].[/]-log: Initializing Spring embedded WebApplicationContext
2025-08-11 21:38:56.504 INFO org.springframework.web.context.ContextLoader-prepareWebApplicationContext: Root WebApplicationContext: initialization completed in 961 ms
2025-08-11 21:38:56.521 INFO org.springframework.boot.web.servlet.ServletRegistrationBean-addRegistration: Servlet dispatcherServlet mapped to [/]
2025-08-11 21:38:56.522 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'characterEncodingFilter' to: [/*]
2025-08-11 21:38:56.522 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'hiddenHttpMethodFilter' to: [/*]
2025-08-11 21:38:56.522 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'httpPutFormContentFilter' to: [/*]
2025-08-11 21:38:56.522 INFO org.springframework.boot.web.servlet.FilterRegistrationBean-configure: Mapping filter: 'requestContextFilter' to: [/*]
2025-08-11 21:38:56.995 INFO com.benzhitech.manager.SmsManager-<clinit>: 启动验证码定时清理任务！
2025-08-11 21:38:57.163 INFO com.benzhitech.manager.SmsManager-init: 启动验证码发送失败补发线程！
2025-08-11 21:38:57.218 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:38:57.279 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter-initControllerAdviceCache: Looking for @ControllerAdvice: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@651837bf: startup date [Mon Aug 11 21:38:55 CST 2025]; root of context hierarchy
2025-08-11 21:38:57.293 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/index]}" onto public org.springframework.web.servlet.ModelAndView com.benzhitech.controller.IndexController.hello(org.springframework.web.servlet.ModelAndView)
2025-08-11 21:38:57.294 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsage]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsage(java.lang.String,java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:38:57.295 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsed]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsed(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:38:57.295 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUsedInMonth]}" onto public java.lang.String com.benzhitech.controller.MeterController.getUsedInMonth(java.lang.String,int,java.lang.String,java.lang.String)
2025-08-11 21:38:57.296 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getMeters]}" onto public java.lang.String com.benzhitech.controller.MeterController.getMeters(java.lang.String)
2025-08-11 21:38:57.297 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/prepay]}" onto public java.lang.String com.benzhitech.controller.OrderController.getPrePayId(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.297 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/allin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.allinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse) throws java.io.IOException
2025-08-11 21:38:57.298 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/weixin/callback]}" onto public synchronized void com.benzhitech.controller.OrderController.weixinNotify(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:38:57.298 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/info]}" onto public java.lang.String com.benzhitech.controller.OrderController.getTaxInfo(long)
2025-08-11 21:38:57.298 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/tax/open]}" onto public java.lang.String com.benzhitech.controller.OrderController.openInvoice(long,java.lang.String,int,java.lang.String,java.lang.String,java.lang.String,java.lang.String)
2025-08-11 21:38:57.300 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getOrders]}" onto public java.lang.String com.benzhitech.controller.OrderController.getOrders(long,java.lang.String)
2025-08-11 21:38:57.301 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/project/config]}" onto public java.lang.String com.benzhitech.controller.ProjectController.getProjectConfig()
2025-08-11 21:38:57.317 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/bind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.bind(java.lang.String,long,java.lang.String,java.lang.String)
2025-08-11 21:38:57.318 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/deleteBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(long,long)
2025-08-11 21:38:57.319 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/unbind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.unbind(java.lang.String,long,java.lang.String,java.lang.String,int)
2025-08-11 21:38:57.319 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBinds]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBinds(long)
2025-08-11 21:38:57.319 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUnBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUnBindCode(long,java.lang.String)
2025-08-11 21:38:57.320 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindCode]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindCode(long,java.lang.String,java.lang.String)
2025-08-11 21:38:57.321 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/addBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.addBind(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.321 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getUserBind]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getUserBind(long,java.lang.String)
2025-08-11 21:38:57.322 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getBindInfoFilter]}" onto public java.lang.String com.benzhitech.controller.UserBindController.getBindInfoFilter(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.323 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/register]}" onto public java.lang.String com.benzhitech.controller.UserController.register(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.324 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getToken]}" onto public java.lang.String com.benzhitech.controller.UserController.getToken(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.325 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getPhone]}" onto public java.lang.String com.benzhitech.controller.UserController.getPhone(java.util.Map<java.lang.String, java.lang.String>)
2025-08-11 21:38:57.326 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/api/getRegisterCode]}" onto public java.lang.String com.benzhitech.controller.UserController.getRegisterCode(java.lang.String,java.lang.String)
2025-08-11 21:38:57.330 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-08-11 21:38:57.331 INFO org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping-register: Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-08-11 21:38:57.347 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:38:57.348 INFO org.springframework.web.servlet.handler.SimpleUrlHandlerMapping-registerHandler: Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-08-11 21:38:57.385 INFO org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping-<init>: Adding welcome page template: index
2025-08-11 21:38:57.414 INFO org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer-postProcessTemplateLoaders: ClassTemplateLoader for Spring macros added to FreeMarker configuration
2025-08-11 21:38:57.518 INFO org.springframework.boot.devtools.autoconfigure.OptionalLiveReloadServer-startServer: LiveReload server is running on port 35729
2025-08-11 21:38:57.553 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-afterSingletonsInstantiated: Registering beans for JMX exposure on startup
2025-08-11 21:38:57.554 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-autodetect: Bean with name 'dataSource' has been autodetected for JMX exposure
2025-08-11 21:38:57.558 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-registerBeanInstance: Located MBean 'dataSource': registering with JMX server as MBean [com.zaxxer.hikari:name=dataSource,type=HikariDataSource]
2025-08-11 21:38:57.567 INFO org.apache.coyote.http11.Http11NioProtocol-log: Starting ProtocolHandler ["http-nio-8181"]
2025-08-11 21:38:57.569 INFO org.apache.tomcat.util.net.NioSelectorPool-log: Using a shared selector for servlet write/read
2025-08-11 21:38:57.574 INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer-start: Tomcat started on port(s): 8181 (http) with context path ''
2025-08-11 21:38:57.576 INFO com.benzhitech.App-logStarted: Started App in 2.116 seconds (JVM running for 571.33)
2025-08-11 21:38:57.581 INFO org.springframework.boot.devtools.autoconfigure.ConditionEvaluationDeltaLoggingListener-onApplicationEvent: Condition evaluation unchanged
2025-08-11 21:39:01.076 INFO org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-doClose: Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@651837bf: startup date [Mon Aug 11 21:38:55 CST 2025]; root of context hierarchy
2025-08-11 21:39:01.079 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-destroy: Unregistering JMX-exposed beans on shutdown
2025-08-11 21:39:01.079 INFO org.springframework.jmx.export.annotation.AnnotationMBeanExporter-unregisterBeans: Unregistering JMX-exposed beans
