2025-08-26 16:05:42.984 WARN org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-refresh: Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appConfigurer': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestHandlerInterceptor' defined in file [D:\code\benzhi\feeservice\target\classes\com\benzhitech\common\interceptor\RequestHandlerInterceptor.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.benzhitech.common.interceptor.RequestHandlerInterceptor] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@2bc1adad]
2025-08-26 16:06:24.925 WARN org.mybatis.spring.mapper.ClassPathMapperScanner-warn: No MyBatis mapper was found in '[com.benzhitech]' package. Please check your configuration.
2025-08-26 16:06:25.454 WARN org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-refresh: Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [org/mybatis/spring/boot/autoconfigure/MybatisAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [D:\code\benzhi\feeservice\target\classes\mapper\LoginMapper.xml]'; nested exception is org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [D:\code\benzhi\feeservice\target\classes\mapper\LoginMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.benzhitech.model.LoginDO'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.benzhitech.model.LoginDO
2025-08-26 16:06:35.730 WARN org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-refresh: Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'meterManager': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.benzhitech.remote.RemoteUserProxy' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}
