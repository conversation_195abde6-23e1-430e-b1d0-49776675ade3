2025-08-11 21:38:53.777 WARN org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext-refresh: Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'appConfigurer': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestHandlerInterceptor' defined in file [D:\code\benzhi\feeservice\target\classes\com\benzhitech\common\interceptor\RequestHandlerInterceptor.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.benzhitech.common.interceptor.RequestHandlerInterceptor] from ClassLoader [org.springframework.boot.devtools.restart.classloader.RestartClassLoader@2eaab7ef]
