USE [feemaster]
GO
/****** Object:  Table [dbo].[user_bind]    Script Date: 07/21/2023 11:37:33 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[user_bind](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[user_id] [bigint] NOT NULL,
	[wx_id] [varchar](50) NULL,
	[project_id] [bigint] NOT NULL,
	[m_id] [varchar](50) NOT NULL,
	[render] [varchar](50) NOT NULL,
	[addr] [varchar](150) NULL,
	[phone] [varchar](50) NOT NULL,
	[bind] [int] NOT NULL,
	[gmt_created] [datetime] NOT NULL,
	[gmt_modified] [datetime] NOT NULL,
	[deleted] [int] NOT NULL,
 CONSTRAINT [PK_user_ammeter] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[fee_user]    Script Date: 07/21/2023 11:37:33 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[fee_user](
	[id] [bigint] NOT NULL,
	[user_name] [varchar](50) NOT NULL,
	[wx_name] [varchar](50) NULL,
	[wx_id] [varchar](50) NOT NULL,
	[project_id] [bigint] NULL,
	[phone] [varchar](50) NULL,
	[gmt_created] [datetime] NOT NULL,
	[gmt_modified] [datetime] NOT NULL,
	[wx_session] [varchar](50) NULL,
 CONSTRAINT [PK_fee_user] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[fee_order]    Script Date: 07/21/2023 11:37:33 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[fee_order](
	[id] [bigint] NOT NULL,
	[user_id] [bigint] NOT NULL,
	[project_id] [int] NOT NULL,
	[m_id] [varchar](50) NOT NULL,
	[wx_id] [varchar](50) NULL,
	[status] [smallint] NOT NULL,
	[fit_status] [smallint] NOT NULL,
	[amount] [int] NOT NULL,
	[prepay_id] [varchar](50) NULL,
	[wx_trade_no] [varchar](50) NULL,
	[version] [int] NULL,
	[gmt_created] [datetime] NULL,
	[gmt_modified] [datetime] NULL,
	[creator] [varchar](50) NULL
) ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  Table [dbo].[fee_login]    Script Date: 07/21/2023 11:37:33 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[fee_login](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[user_id] [bigint] NOT NULL,
	[wx_id] [varchar](50) NULL,
	[expire] [datetime] NULL,
	[device] [text] NULL,
	[gmt_created] [datetime] NULL,
	[gmt_modified] [datetime] NULL,
 CONSTRAINT [PK_fee_login] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
/****** Object:  Default [DF_user_ammeter_deleted]    Script Date: 07/21/2023 11:37:33 ******/
ALTER TABLE [dbo].[user_bind] ADD  CONSTRAINT [DF_user_ammeter_deleted]  DEFAULT ((0)) FOR [deleted]
GO

/**** fee_order表增加索引*****/
ALTER TABLE fee_order ADD CONSTRAINT pk_id PRIMARY KEY (id);
CREATE INDEX idx_user_id ON fee_order (user_id);
CREATE INDEX idx_m_id ON fee_order (m_id);

/**** fee_user表增加索引*****/
/** ALTER TABLE fee_user ADD CONSTRAINT pk_id PRIMARY KEY (id); **/
CREATE INDEX idx_wx_id ON fee_user (wx_id);
CREATE INDEX idx_phone ON fee_user (phone);

/**** user_bind 表增加索引*****/
/** ALTER TABLE user_bind ADD CONSTRAINT pk_id PRIMARY KEY (id); **/
CREATE INDEX idx_user_id ON user_bind (user_id);
CREATE INDEX idx_m_id ON user_bind (m_id);

