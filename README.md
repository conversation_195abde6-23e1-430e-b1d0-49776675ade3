# feeservice

小指尖小程序服务端

CREATE TABLE tax_title (
    id BIGINT PRIMARY KEY COMMENT '主键ID',
    project_id BIGINT COMMENT '项目ID',
    user_id BIGINT COMMENT '用户ID',
    tax_no VARCHAR(255) COMMENT '公司税号',
    tax_name VARCHAR(255) COMMENT '公司名称',
    remark VARCHAR(255) COMMENT '发票备注',
    email VARCHAR(255) COMMENT '发票接收邮箱',
    INDEX idx_user_project (user_id, project_id)
) COMMENT = '税务信息表';

ALTER TABLE `order`
ADD COLUMN invoice_state INT DEFAULT 1 COMMENT '税单状态: -1(过期无法开票), 0-开票失败, 1-未开票(默认), 2-开票中, 3-开票成功',
ADD COLUMN tax_type INT COMMENT '税单类型: 1-增值税专用发票, 2-增值税普通发票',
ADD COLUMN invoice_file VARCHAR(255) COMMENT '发票文件地址';