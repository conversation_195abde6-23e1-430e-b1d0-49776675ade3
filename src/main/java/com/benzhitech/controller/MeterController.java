package com.benzhitech.controller;

import java.text.ParseException;
import java.util.List;

import javax.annotation.Resource;

import com.benzhitech.common.ResultCode;
import com.benzhitech.manager.MeterManager;
import com.benzhitech.model.ProjectDO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.benzhitech.common.Result;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.remote.RemoteUserProxy;
import com.benzhitech.remote.model.Meter;


@Controller
@RequestMapping(value = "/api")
public class MeterController {
	@Resource
	private RemoteUserProxy remoteUserProxy;

	@Resource
	private MeterManager meterManager;
	
	/**
	 * 获取用户表列表
	 * @param map
	 * @return
	 */
	@RequestMapping(value="/getMeters")
	@ResponseBody
	public String getMeters(@RequestParam(name = "m_id") String mId){
		List<Meter> list = remoteUserProxy.getMeters(ContextHolder.get().getProjectDO(), mId);
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		result.setData(list);
		return JSON.toJSONString(result);
	}
	@RequestMapping(value="/getUsedInMonth")
	@ResponseBody
	public String getUsedInMonth(
			@RequestParam(name = "meter_id",required = false) String meterId,
			@RequestParam(name = "meter_type") int meterType,
			@RequestParam(name = "start",required = false) String start,
			@RequestParam(name = "end",required = false) String end
	){
		JSONArray array = meterManager.getUsedInMonth(meterId, meterType);
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		result.setData(array==null|| array.isEmpty() ?null:array.get(0));
		return JSON.toJSONString(result);
	}
	/**
	 * 获取使用量和扣费记录
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(value="/getUsed")
	@ResponseBody
	public String getUsed(
			@RequestParam(name = "meter_id") String meterId,
			@RequestParam(name = "meter_type") int meterType,
			@RequestParam(name = "start",required = false) String start,
			@RequestParam(name = "end",required = false) String end
			){
		JSONArray array = remoteUserProxy.getFeeDetail(ContextHolder.get().getProjectDO(),meterId, meterType,start,end);
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		result.setData(array==null|| array.isEmpty() ?null:array.get(0));
		return JSON.toJSONString(result);
	}

	/**
	 * 获取使用量，这里没有扣费
	 * @param JSON
	 * @return
	 * @throws ParseException
	 */
	@RequestMapping(value="/getUsage")
	@ResponseBody
	public String getUsage(
			@RequestParam(name = "m_id",required = false) String mId,
			@RequestParam(name = "meter_id", required = false) String meterId,
			@RequestParam(name = "meter_type") int meterType,
			@RequestParam(name = "start") String start,
			@RequestParam(name = "end") String end
			){
		Result<Object> result = new Result<Object>();
		// 检查至少有一个参数被提供
		if (mId == null && meterId == null) {
			result.setCode(ResultCode.PARAMETER_ERROR);
			return JSON.toJSONString(result);
		}
		ProjectDO project = ContextHolder.get().getProjectDO();
		if(project.getId()!=3){
			result.setCode(ResultCode.SYSTEM_FORBIDDEN);
			return JSON.toJSONString(result);
		}
		JSONArray arry = remoteUserProxy.getUsedDetail(project,mId,meterId,meterType,start,end);
		result.setSuccess(true);
		result.setData(arry==null|| arry.isEmpty() ?null:arry.get(0));
		return JSON.toJSONString(result);
	}

}
