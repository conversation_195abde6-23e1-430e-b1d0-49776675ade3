package com.benzhitech.controller;


import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.manager.SmsManager;
import com.benzhitech.manager.UserManager;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.RemoteUserProxy;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping(value = "/api")
public class ProjectController {
	@Resource
	private RemoteUserProxy remoteUserProxy;
	
	@RequestMapping("/project/config")
	@ResponseBody
	public String getProjectConfig() {
		ProjectDO project = ContextHolder.get().getProjectDO();
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		Map<String,Object> map = new HashMap<String,Object>();
		if(project.isShowPrice()){
			Float price =remoteUserProxy.getPowerPrice(project);
			map.put("price",price);
		}
		result.setData(map);
		return JSON.toJSONString(result);
	}
}
