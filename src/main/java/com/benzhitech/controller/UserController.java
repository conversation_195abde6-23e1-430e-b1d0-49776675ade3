package com.benzhitech.controller;



import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.ResultCode;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.manager.UserManager;
import com.benzhitech.manager.SmsManager;
import com.benzhitech.model.IDGenerater;
import com.benzhitech.model.UserDO;
import com.benzhitech.remote.RemoteUserProxy;
import com.benzhitech.remote.wx.util.GetOpenIDUtil;

@Controller
@RequestMapping(value = "/api")
public class UserController {
	@Resource
	private UserManager userManager;
	@Resource
	private SmsManager smsManager;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	/**
	 * 获取用户登录token
	 * @param map
	 * @return
	 */
	@RequestMapping("/getToken")
	@ResponseBody
	public String getToken(@RequestParam Map<String,String> map){
		String wxCode = map.get("wx_code");
		Result<Object> result  =null;
		if(!StringUtil.isLegalParameter(wxCode)) {
			result = new Result<Object>();
			result.setSuccess(false);
			result.setCode(ResultCode.PARAMETER_ILLEGAL);
		}else {
			result = userManager.getToken(wxCode);
		}
		return JSON.toJSONString(result);
	}
	
	/**
	 * 获取微信用户手机号
	 * @param map
	 * @return
	 */
	@RequestMapping("/getPhone")
	@ResponseBody
	public String getPhone(@RequestParam Map<String,String> map){
		String phone = GetOpenIDUtil.decryptPhone(map.get("wx_skey"),map.get("wx_iv"),map.get("wx_data"));
		return phone;
	}
	/**
	 * 注册时发送手机验证码
	 * @param wxId
	 * @param phone
	 * @return
	 */
	@RequestMapping("/getRegisterCode")
	@ResponseBody
	public String getRegisterCode(@RequestParam(name = "wx_id") String wxId,@RequestParam(name = "phone") String phone){
		Result<Boolean> result = new Result<Boolean>();
		if(!StringUtil.isPhone(phone)){
			result.setData(false);
			result.setCode(ResultCode.NOT_PHONE_NUMBER);
		}else if(userManager.isRegister(phone,wxId)){
			result.setData(false);
			result.setCode(ResultCode.PHONE_HAS_REGISTER);
		}else{
			result.setData(smsManager.sendCode(ContextHolder.get().getProjectDO(),phone));
			result.setSuccess(true);			
		}
		return JSON.toJSONString(result);
	}
	/**
	 * 用户注册
	 * @return
	 */
	@RequestMapping("/register")
	@ResponseBody
	public String register(@RequestParam Map<String,String> map) {
		String wxId = map.get("wx_id");
		String phoneCode = map.get("phone_code");
		String phone = map.get("phone");
		//String mId = map.get("m_id"); 注册时，不绑定户号或者电表了
		String sessionKey = map.get("wx_skey");
		Result<Object> result = new Result<Object>();
		//判断手机号
		if(!StringUtil.isPhone(phone)) {
			result.setData(false);
			result.setCode(ResultCode.NOT_PHONE_NUMBER);
			new Result<Object>();
		}
		//判断验证码是否有效
		if(!smsManager.validate(phone, phoneCode)){
			result.setSuccess(false);
			result.setCode(ResultCode.PHONE_CODE_ERROR);
			return JSON.toJSONString(result);
		}
		long projectId = ContextHolder.get().getProjectDO().getId();
		UserDO user = new UserDO();
		user.setUserName(phone);
		user.setWxId(wxId);
		user.setProjectId(projectId);
		user.setPhone(phone);
		user.setWxSession(sessionKey);
		user.setGmtCreated(new Date());
		user.setGmtModified(new Date());
		user.setId(IDGenerater.generateUserId());
		
		result = userManager.register(user);
		
		BizLog.log(projectId,user.getId(), POINT.USER, ACT.REGISTER, result.isSuccess(), user.getPhone());
		
		return JSON.toJSONString(result);
	}
	
}
