package com.benzhitech.controller;



import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.ResultCode;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.manager.UserBindManager;
import com.benzhitech.manager.UserManager;
import com.benzhitech.manager.SmsManager;
import com.benzhitech.mapper.UserBindMapper;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.model.UserBindDO;
import com.benzhitech.model.UserDO;
import com.benzhitech.remote.RemoteUserProxy;
import com.benzhitech.remote.model.MUser;

import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping(value = "/api")
@Slf4j
public class UserBindController {
	@Resource
	private UserBindManager userBindManager;
	@Resource
	private SmsManager smsManager;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	@Resource
	private UserBindMapper userBindMapper;
	@Resource
	private UserManager userManager;
	/**
	 * 注册前查询商户绑定信息
	 * @return
	 */
	@RequestMapping("/getBindInfoFilter")
	@ResponseBody
	public String getBindInfoFilter(@RequestParam Map<String,String> map){
		String wxId = map.get("wx_id");
		//String phoneCode = map.get("phone_code");
		//String phone = map.get("phone");
		String mId = map.get("m_id").trim().replaceAll("－", "-");
		//String token = map.get("token");
		String act = map.get("act");
		Result<Object> result = new Result<Object>();
		//没有token说明是第一次注册查询，需要验证码
//		if(StringUtil.isEmpty(token) && !smsManager.validate(phone, phoneCode)){
//			result.setSuccess(false);
//			result.setCode(ResultCode.PHONE_CODE_ERROR);
//			log.warn("验证码错误,相关参数："+JSON.toJSONString(map));
//			return JSON.toJSONString(result);
//		}
		//如果是后面增加，查询判断是否已增加过
		ProjectDO project = ContextHolder.get().getProjectDO();
		
		
		if("add".equals(act)){
			UserBindDO ub = userBindManager.getUserBind(Long.parseLong(map.get("user_id")), project.getId(), mId);
			if(ub!=null){
				result.setSuccess(false);
				result.setCode(ResultCode.BIND_IS_EXSIT);
				return JSON.toJSONString(result);				
			}
		}
		//查询项目信息 获取项目接口地址 projectId
		Map<String,String> filterMap = new HashMap<String,String>();
		MUser mUser = remoteUserProxy.getUserInfo(project,mId);
		if(mUser!=null){
			filterMap.put("userName", StringUtil.filterInfo(mUser.getRender(), 1, 0));
			filterMap.put("addr", mUser.getAddr());
			//解除绑定查询时，需要返回的是当前用户手机号，而非商户手机号
			if("unbind".equals(act)){
				UserDO user = userManager.getUserByWxId(wxId);
				filterMap.put("phone", user.getPhone());
			//如果是绑定，需要验证的是商户手机
			}else{
				filterMap.put("phone", StringUtil.filterInfo(mUser.getPhone(), 3, 3));
			}
			filterMap.put("mId", mId);
			filterMap.put("projectId", project.getId().toString());
			result.setData(filterMap);
			result.setSuccess(true);
		}else{
			result.setCode(ResultCode.REMOTE_USER_NOT_EXSIT);
		}
		
		return JSON.toJSONString(result);
	}
	/**
	 * 查询已关联的商户电表信息
	 * @param userId
	 * @param mId
	 * @return
	 */
	@RequestMapping("/getUserBind")
	@ResponseBody
	public String getUserBind(@RequestParam(name = "user_id") long userId,@RequestParam(name = "m_id") String mId){
		Result<Object> result = new Result<Object>();
		ProjectDO project = ContextHolder.get().getProjectDO();
		UserBindDO ub = userBindManager.getUserBind(userId, project.getId(), mId.trim());
		if(ub==null){
			result.setSuccess(false);
			result.setCode(ResultCode.BIND_NOT_EXSIT);
			return JSON.toJSONString(result);	
		}
		float amount = remoteUserProxy.getAmount(project, mId);
		//不能用数据库里记录的UserBindDO.phone，户主会变化，手机号也会变，以为主站的为准20240823
		MUser mUser = remoteUserProxy.getUserInfo(project,mId);
		if(mUser==null){
			result.setSuccess(false);
			result.setCode(ResultCode.REMOTE_USER_NOT_EXSIT);
			return JSON.toJSONString(result);
		}
		Map<String,Object> filterMap = new HashMap<String,Object>();
		
		filterMap.put("userName", ub.getBind()==UserBindDO.BIND_NO?StringUtil.filterInfo(ub.getRender(), 1, 0):ub.getRender());
		filterMap.put("addr", ub.getAddr());
		filterMap.put("phone", ub.getBind()==UserBindDO.BIND_NO?StringUtil.filterInfo(mUser.getPhone(), 3, 3):mUser.getPhone());
		filterMap.put("mId", mId);
		filterMap.put("amount", amount);
		filterMap.put("projectId", project.getId());
		filterMap.put("locked", mUser.isLocked());
		
		result.setData(filterMap);
		result.setSuccess(true);
		return JSON.toJSONString(result);
	}
	/**
	 * 绑定时发送手机验证码
	 * @return
	 */
	@RequestMapping("/getBindCode")
	@ResponseBody
	public String getBindCode(@RequestParam(name = "user_id") long userId,
			@RequestParam(name = "wx_id") String wxId,
			@RequestParam(name = "m_id") String mId){
		
		Result<Boolean> result = new Result<Boolean>();
		//，绑定时，应该发送商户的手机号短信验证码，而不是用户手机号验证码
		ProjectDO project = ContextHolder.get().getProjectDO();
		UserBindDO ub = userBindManager.getUserBind(userId, project.getId(), mId.trim());
		if(ub==null){
			result.setCode(ResultCode.BIND_NOT_EXSIT);
			return JSON.toJSONString(result);
		}
		//还是直接调主站后台获取吧，户号下面户主会变的，以主站为准 20240823
		MUser user = remoteUserProxy.getUserInfo(project, ub.getMId());

		if(user==null || !StringUtil.isPhone(user.getPhone())) {
			result.setCode(ResultCode.BIND_NOT_EXSIT_PHONE);
			return JSON.toJSONString(result);
		}
		result.setData(smsManager.sendCode(project,user.getPhone()));
		result.setSuccess(true);
		result.setData(true);
		return JSON.toJSONString(result);
	}

	/**
	 * 解除绑定时发送手机验证码
	 * @param userId
	 * @return
	 */
	@RequestMapping("/getUnBindCode")
	@ResponseBody
	public String getUnBindCode(@RequestParam(name = "user_id") long userId,
			@RequestParam(name = "wx_id") String wxId){
		
		Result<Boolean> result = new Result<Boolean>();
		UserDO user = userManager.getUserByWxId(wxId);
		if(user==null){
			result.setCode(ResultCode.USER_NOT_REGISTER);
			return JSON.toJSONString(result);
		}
		//解绑时，发送的是微信用户手机号
		result.setData(smsManager.sendCode(ContextHolder.get().getProjectDO(),user.getPhone()));
		result.setSuccess(true);
		result.setData(true);
		return JSON.toJSONString(result);
	}	
	/**
	 * 绑定商户
	 * @param code
	 * @param userId
	 * @param wxId
	 * @return
	 */
	@RequestMapping("/bind")
	@ResponseBody
	public String bind(@RequestParam(name = "phone_code") String code,
			@RequestParam(name = "user_id") long userId,@RequestParam(name = "wx_id") 
			String wxId,@RequestParam(name = "m_id") String mId){
		Result<Boolean> result = new Result<Boolean>();
		//查看是否已存在记录，这里查出来的UserBindDO.phone是商户手机号
		ProjectDO project = ContextHolder.get().getProjectDO();
		UserBindDO ub = userBindManager.getUserBind(userId,project.getId(), mId.trim().replaceAll("－", "-"));
		if(ub==null){
			result.setCode(ResultCode.BIND_NOT_EXSIT);
			return JSON.toJSONString(result);
		}
		//还是直接调主站后台获取吧，户号下面户主会变的，以主站为准 20240823
		MUser user = remoteUserProxy.getUserInfo(project, ub.getMId());
		//判断验证码是否有效，绑定时发送的是商户手机号
		if(!smsManager.validate(user.getPhone(), code)){
			result.setSuccess(false);
			result.setCode(ResultCode.PHONE_CODE_ERROR);
			return JSON.toJSONString(result);
		}
		ub.setBind(UserBindDO.BIND_OK);
		ub.setGmtModified(new Date());
		result.setData(userBindManager.bind(ub));
		result.setSuccess(true);
		BizLog.log(project.getId(), userId, POINT.USER, ACT.BIND, result.isSuccess(), JSON.toJSONString(ub));
		return JSON.toJSONString(result);
	}
	/**
	 * 解除绑定商户
	 * @param code
	 * @param userId
	 * @param wxId
	 * @param mId
	 * @param deleted
	 * @return
	 */
	@RequestMapping("/unbind")
	@ResponseBody
	public String unbind(@RequestParam(name = "phone_code") String code,
			@RequestParam(name = "user_id") long userId,
			@RequestParam(name = "wx_id") 
			String wxId,@RequestParam(name = "m_id") String mId,
			@RequestParam(name = "deleted") int deleted){
		Result<Boolean> result = new Result<Boolean>();
		//查看用户绑定记录是否存在
		long projectId = ContextHolder.get().getProjectDO().getId();
		UserBindDO ub= userBindManager.getUserBind(userId, projectId, mId.trim().replaceAll("－", "-"));
		
		if(ub == null){
			result.setCode(ResultCode.BIND_NOT_EXSIT);
			return JSON.toJSONString(result);
		}
		//解绑定是微信注册手机号，不是商户手机号
		UserDO user = userManager.getUserByWxId(wxId);
		//判断验证码是否有效
		if(!smsManager.validate(user.getPhone(), code)){
			result.setSuccess(false);
			result.setCode(ResultCode.PHONE_CODE_ERROR);
			return JSON.toJSONString(result);
		}
		ub.setBind(UserBindDO.BIND_NO);
		ub.setGmtModified(new Date());
		if(deleted==UserBindDO.DELETED){
			ub.setDeleted(UserBindDO.DELETED);
		}
		result.setData(userBindManager.unbind(ub));
		result.setSuccess(true);
		
		BizLog.log(projectId, userId, POINT.USER, ACT.UNBIND, result.isSuccess(), JSON.toJSONString(ub));
		
		return JSON.toJSONString(result);
	}
	
	/**
	 *    增加用户关联
	 * @param map
	 * @return
	 */
	@RequestMapping("/addBind")
	@ResponseBody
	public String addBind(@RequestParam Map<String,String> map){
		String wxId = map.get("wx_id");
		String mId = map.get("m_id").trim().replaceAll("－", "-");
		long userId = map.get("user_id")==null?0:Long.parseLong(map.get("user_id"));
		ProjectDO project =ContextHolder.get().getProjectDO();
		Result<Object> result = new Result<Object>();
		if(userId==0 || StringUtil.isEmpty(mId)) {
			result.setSuccess(false);
			result.setCode(ResultCode.PARAMETER_ERROR);
			return JSON.toJSONString(result);
		}
		//查询用户信息
		UserDO user = userManager.getUserById(userId);
		if(user==null){
			result.setSuccess(false);
			result.setCode(ResultCode.USER_NOT_REGISTER);
			return JSON.toJSONString(result);
		}

		//增加判断，防止并发插入两条记录
		UserBindDO ub = userBindManager.getUserBind(userId, project.getId(), mId);
		if(ub!=null){
			result.setSuccess(false);
			result.setCode(ResultCode.BIND_IS_EXSIT);
			return JSON.toJSONString(result);
		}

		//插入用户关联记录
		ub = new UserBindDO();
		ub.setUserId(userId);
		ub.setWxId(wxId);
		ub.setProjectId(project.getId());
		MUser mUser = remoteUserProxy.getUserInfo(project,mId);
		
		//ub.setMId(mId); 用户输入的可能有全角，或者小写
		ub.setMId(mUser.getMId());
		
		ub.setRender(mUser.getRender());
		ub.setPhone(mUser.getPhone());//其实这里记录没啥用，这个手机号，会随户主变更而变化的
		//用户手机号和商户号相同，则默认绑定
		ub.setBind(user.getPhone().equals(mUser.getPhone())?UserBindDO.BIND_OK:UserBindDO.BIND_NO);
		ub.setAddr(mUser.getAddr());
		ub.setGmtCreated(new Date());
		ub.setGmtModified(new Date());
		int num = userBindMapper.addUserBind(ub);
		if(num==0){
			result.setSuccess(false);
			result.setCode(ResultCode.SYSTEM_ERROR);
		}else{
			result.setSuccess(true);
			result.setData(ub);			
		}
		BizLog.log(project.getId(), userId, POINT.USER, ACT.ADDBIND, result.isSuccess(), JSON.toJSONString(ub));
		return JSON.toJSONString(result);
	}
	/**
	 * 	删除关联
	 * @param userId
	 * @param id
	 * @return
	 */
	@RequestMapping("/deleteBind")
	@ResponseBody
	public String unbind(@RequestParam(name = "user_id") long userId,@RequestParam(name = "id") long id){
		Result<Boolean> result = new Result<Boolean>();
		result.setSuccess(true);
		result.setData(userBindManager.deleteBind(id, userId));	
		return JSON.toJSONString(result);
	}
	/**
	 * 获取用户关联列表
	 * @param userId
	 * @return
	 */
	@RequestMapping("/getUserBinds")
	@ResponseBody
	public String getBinds(@RequestParam(name = "user_id") long userId){
		List<UserBindDO> list = userBindManager.getUserBinds(userId);
		Result<List<UserBindDO>> result = new Result<List<UserBindDO>>();
		result.setSuccess(true);
		result.setData(filterInfo(list));
		return JSON.toJSONString(result);
	}
	/**
	 * 未绑定的过滤掉关键信息
	 * @param list
	 */
	private List<UserBindDO> filterInfo(List<UserBindDO> list){
		if(list!=null && list.size()>0){
			for(UserBindDO ua:list){
				if(ua.getBind()==UserBindDO.BIND_NO){
					ua.setRender(StringUtil.filterInfo(ua.getRender(), 1, 0));
					ua.setPhone(StringUtil.filterInfo(ua.getPhone(), 3, 3));
				}
			}
		}
		return list;
	}
	
}
