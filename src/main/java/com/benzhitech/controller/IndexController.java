package com.benzhitech.controller;


import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.benzhitech.manager.UserManager;
import com.benzhitech.manager.SmsManager;
import com.benzhitech.remote.RemoteUserProxy;
@Controller
@RequestMapping(value = "/")
public class IndexController {
	@Resource
	private UserManager userManager;
	@Resource
	private SmsManager smsManager;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	
	@RequestMapping("/index")
	@ResponseBody
	public ModelAndView hello(ModelAndView m) {
		userManager.equals(null);
		smsManager.equals(null);
		remoteUserProxy.equals(null);
		m.addObject("hello", "Fee Master System");
		m.setViewName("index");
		return m;
	}
}
