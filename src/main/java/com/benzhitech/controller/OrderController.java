package com.benzhitech.controller;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.benzhitech.mapper.TaxTitleMapper;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.model.TaxTitleDO;
import com.benzhitech.remote.allinpay.AllInPayProxy;
import com.benzhitech.remote.allinpay.util.SybUtil;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.ResultCode;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.manager.OrderManager;
import com.benzhitech.model.OrderDO;
import com.benzhitech.remote.RemoteUserProxy;

import lombok.extern.slf4j.Slf4j;

@Controller
@RequestMapping(value = "/api")
@Slf4j
public class OrderController {
	@Resource
	private OrderManager orderManager;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	@Resource
	private TaxTitleMapper  taxTitleMapper;
	
	/**
	 * 获取预支付订单
	 * @param map
	 * @return
	 */
	@RequestMapping(value="/prepay")
	@ResponseBody
	public String getPrePayId(@RequestParam Map<String,String> map){
		String wxId = map.get("wx_id");
		String mId = map.get("m_id");
		long userId = Long.parseLong(map.get("user_id"));
		double amount = map.get("amount")==null?0.00:Double.parseDouble(map.get("amount"));

		if(amount<=0.00) {
			Result<Boolean> res = new Result<Boolean>();
			res.setCode(ResultCode.PAY_NUMBER_ERROR);
			return JSON.toJSONString(res);
		}
		
		Result<Map<String,String>> res = orderManager.createOrder(userId, wxId, mId, StringUtil.changeY2F(amount));
		
		return JSON.toJSONString(res);
	}
	@RequestMapping(value="/allin/callback")
	@ResponseBody
	public synchronized void allinNotify(HttpServletRequest request,HttpServletResponse response) throws IOException {
		System.out.println("通联支付收到请求，时间："+System.currentTimeMillis());
		request.setCharacterEncoding("UTF-8");//通知传输的编码为GBK
		response.setCharacterEncoding("UTF-8");
		TreeMap<String,String> params = getParams(request);//动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容
		String result = orderManager.allInNotify(params);
		response.getOutputStream().write(result.getBytes());
		response.flushBuffer();
	}
	/**
	 * 动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容由于收银宝加字段而引起的签名异常
	 * @param request
	 * @return
	 */
	private TreeMap<String, String> getParams(HttpServletRequest request){
		TreeMap<String, String> map = new TreeMap<String, String>();
		Map reqMap = request.getParameterMap();
		for(Object key:reqMap.keySet()){
			String value = ((String[])reqMap.get(key))[0];
			System.out.println(key+";"+value);
			map.put(key.toString(),value);
		}
		return map;
	}
	@RequestMapping(value="/weixin/callback")
	@ResponseBody
	public synchronized void weixinNotify(HttpServletRequest request,HttpServletResponse response){
		System.out.println("微信支付收到请求，时间："+System.currentTimeMillis());
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader((ServletInputStream)request.getInputStream()));
	        String line = null;
	        StringBuilder sb = new StringBuilder();
	        while((line = br.readLine()) != null){
	            sb.append(line);
	        }
	        br.close();
	        //sb为微信返回的xml
	        String notityXml = sb.toString();
	        String returnXml = orderManager.weiXinNotify(notityXml);
	        if(returnXml!=null){
		        BufferedOutputStream out = new BufferedOutputStream(response.getOutputStream());
		        out.write(returnXml.getBytes());
		        out.flush();
		        out.close();
		        return;
	        }
		} catch (Exception e) {
			log.error("支付回调异常",e);
			
		}
	}

	/**
	 * 目前只有元安里在用
	 * @param userId
	 * @return
	 */

	@RequestMapping(value="/tax/info")
	@ResponseBody
	public String getTaxInfo(@RequestParam(name = "user_id") long userId){
		TaxTitleDO taxTitleDO = taxTitleMapper.queryByUserId(userId, ContextHolder.get().getProjectDO().getId());
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		result.setData(taxTitleDO==null?new TaxTitleDO():taxTitleDO);
		return JSON.toJSONString(result);
	}

	/**
	 * 目前只有元安里和昆明项目在用
	 * @param userId
	 * @param orderId
	 * @param taxType 1 增值税专用发票  2 增值税普通发票
	 * @param taxNo
	 * @param taxName
	 * @param email
	 * @param remark
	 * @return
	 */
	@RequestMapping(value="/tax/open")
	@ResponseBody
	public String openInvoice(
			@RequestParam(name = "user_id") long userId,
			@RequestParam(name = "orderId") String orderId,
			@RequestParam(name = "taxType") int taxType,
			@RequestParam(name = "taxNo", required = false) String taxNo,
			@RequestParam(name = "taxName") String taxName,
			@RequestParam(name = "email") String email,
			@RequestParam(name = "remark",required = false) String remark
	){
		TaxTitleDO taxTitleDO = new TaxTitleDO();
		taxTitleDO.setUserId(userId);
		taxTitleDO.setProjectId(ContextHolder.get().getProjectDO().getId());
		taxTitleDO.setTaxNo(taxNo);
		taxTitleDO.setTaxName(taxName);
		taxTitleDO.setEmail(email);
		taxTitleDO.setRemark(remark);
		Result<Boolean> result = orderManager.openInvoice(orderId, taxType, taxTitleDO);
		return JSON.toJSONString(result);
	}
	/**
	 * 查询商户充值订单记录
	 * @return
	 */
	@RequestMapping(value="/getOrders")
	@ResponseBody
	public String getOrders(@RequestParam(name = "user_id") long userId,@RequestParam(name = "m_id") String mId){
		List<OrderDO> list = orderManager.getOrders(mId);
		Result<Object> result = new Result<Object>();
		result.setSuccess(true);
		result.setData(list);
		return JSON.toJSONString(result);
	}
}
