package com.benzhitech.manager;

import com.alibaba.fastjson.JSONArray;
import com.benzhitech.common.context.Context;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.RemoteUserProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import com.alibaba.fastjson.JSONObject;
import java.util.TreeMap;
import java.util.Map;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Component
@Slf4j
public class MeterManager {
    @Resource
    private RemoteUserProxy remoteUserProxy;
    public static void main(String[] args){
        MeterManager meterManager = new MeterManager();
        RemoteUserProxy r = new RemoteUserProxy();
        ProjectDO project = (new ProjectManager()).getProject(8L);
        meterManager.remoteUserProxy = r;
        try {
            Context context =new Context();
            context.setProjectDO(project);
            ContextHolder.set(context);
            JSONArray json = meterManager.getUsedInMonth("124110608777",0);
            System.out.println(JSONObject.toJSONString(json));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    public JSONArray getUsedInMonth(String meterId,int meterType){
        ProjectDO project = ContextHolder.get().getProjectDO();
        LocalDate now = LocalDate.now();
        LocalDate startOfYear = LocalDate.of(now.getYear(), 1, 1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        String start = startOfYear.format(formatter);
        String end = now.format(formatter);

        JSONArray jsonArray = remoteUserProxy.getFeeDetail(project, meterId, meterType, start, end);

        // 按月汇总 PowerList
        Map<String, JSONObject> monthlySummary = new TreeMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            JSONArray powerList = item.getJSONArray("PowerList");
            for (int j = 0; j < powerList.size(); j++) {
                JSONObject powerItem = powerList.getJSONObject(j);
                String endDate = powerItem.getString("EndDate");
                LocalDate date = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yy-MM-dd"));
                String month = date.format(monthFormatter);

                JSONObject summary = monthlySummary.getOrDefault(month, new JSONObject());
                BigDecimal money = summary.getBigDecimal("money");
                if (money == null) money = BigDecimal.ZERO;
                money = money.add(powerItem.getBigDecimal("money")).setScale(2, RoundingMode.HALF_UP);
                summary.put("money", money);

                BigDecimal useAmount = summary.getBigDecimal("UseAmount");
                if (useAmount == null) useAmount = BigDecimal.ZERO;
                useAmount = useAmount.add(powerItem.getBigDecimal("UseAmount")).setScale(2, RoundingMode.HALF_UP);
                summary.put("UseAmount", useAmount);

                summary.put("EndDate", month);
                monthlySummary.put(month, summary);
            }
        }

        // 构建新的 PowerList
        JSONArray newPowerList = new JSONArray();
        newPowerList.addAll(monthlySummary.values());

        // 更新 jsonArray 中的 PowerList
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject item = jsonArray.getJSONObject(i);
            item.put("PowerList", newPowerList);
        }

        return jsonArray;

    }

}
