package com.benzhitech.manager;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.benzhitech.common.util.StringUtil;
import org.springframework.stereotype.Component;

import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.mapper.UserBindMapper;
import com.benzhitech.mapper.UserMapper;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.model.UserBindDO;
import com.benzhitech.model.UserDO;
import com.benzhitech.remote.RemoteUserProxy;

@Component
public class UserBindManager {
	@Resource
	private ProjectManager projectManager;
	@Resource
	private UserBindMapper userBindMapper;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	@Resource
	private SmsManager smsManager;
	@Resource
	private UserMapper userMapper;
	
	public boolean deleteBind(long id,long userId) {
		ProjectDO project = ContextHolder.get().getProjectDO();
		UserBindDO ub = new UserBindDO();
		ub.setId(id);
		ub.setUserId(userId);
		ub.setProjectId(project.getId());
		ub.setDeleted(UserBindDO.DELETED);
		ub.setGmtModified(new Date());
		boolean b= userBindMapper.updateUserBind(ub)>0;
		BizLog.log(project.getId(), userId, POINT.USER, ACT.DELETEBIND, b, "删除未绑定用户");
		return b;
	}
	public boolean bind(UserBindDO ub){
		return update(ub,true);
	}
	public boolean unbind(UserBindDO ub){
		return update(ub,false);
	}
	public boolean update(UserBindDO ub) {
		if(ub==null || ub.getId()==null)return false;
		return userBindMapper.updateUserBind(ub)>0;
	}
	
	private boolean update(UserBindDO ub,boolean bind){
		ProjectDO project = ContextHolder.get().getProjectDO();
		UserDO user = userMapper.getUserById(ub.getUserId(),project.getId());
		ub.setProjectId(project.getId());
		boolean b = userBindMapper.updateUserBind(ub)>0;
		//发送手机短信通知
		if(b){
			String sign = ContextHolder.get().getProjectDO().getSmsSign();
			smsManager.sendBind(project,user.getPhone(), StringUtil.isEmpty(ub.getRender())?ub.getMId():ub.getRender(),bind);
		}
		return b;
	}
	public UserBindDO getUserBind(long userId,long projectId,String mId){
		Map<String,Object> qmap= new HashMap<String,Object>();
		qmap.put("userId", userId);
		qmap.put("projectId", projectId);
		qmap.put("mId", mId);
		return  userBindMapper.getUserBind(qmap);
	}
	public List<UserBindDO> getUserBinds(long userId){
		Map<String,Object> qmap= new HashMap<String,Object>();
		qmap.put("userId", userId);
		List<UserBindDO> userBinds = userBindMapper.getUserBinds(userId,ContextHolder.get().getProjectDO().getId());
		addRemoteInfo(userBinds);
		return userBinds;
	}
	private void addRemoteInfo(List<UserBindDO> binds) {
		if(binds!=null && !binds.isEmpty()){
			Float powerPrice = null;
			ProjectDO project = ContextHolder.get().getProjectDO();
			if(project.isShowPrice()){
				powerPrice = remoteUserProxy.getPowerPrice(project);
			}
			for(UserBindDO ub:binds){
				//查询余额、电量、电价
                appendInfo(ub, powerPrice);
                ub.setBindText(ub.getBind()==UserBindDO.BIND_OK?"已绑定":"绑定");
				ub.setAmountStatus(ub.getAmount()>0?1:0);
			}
		}
	}
	private void appendInfo(UserBindDO ub,Float powerPrice){
		ProjectDO project = ContextHolder.get().getProjectDO();
		ub.setAmount(remoteUserProxy.getAmount(project, ub.getMId()));
        /* 计算电量，昆明项目需要 */
		if(project.isShowPrice() && powerPrice!=null){
			BigDecimal amount = BigDecimal.valueOf(ub.getAmount());
			BigDecimal price = BigDecimal.valueOf(powerPrice);
			BigDecimal degree = amount.divide(price, 2, RoundingMode.HALF_UP);
			ub.setDegree(degree.floatValue());
		}
	}
}
