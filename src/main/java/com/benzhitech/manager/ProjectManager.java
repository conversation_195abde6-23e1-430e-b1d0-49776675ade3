package com.benzhitech.manager;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.benzhitech.model.ProjectDO;

@Component
public class ProjectManager {
	private static Map<Long,ProjectDO> map = new HashMap<Long,ProjectDO>();
	static{
		ProjectDO p = new ProjectDO();
		p.setId(1L);
		p.setClientKey("BzDemo06f81652460Xf2110s");
		p.setClientSecret("10f2f96d89a32941edf01bxcz0a076f10");
		p.setName("小指尖");
		//对应后台 http://************:34101/  管理员  1
		p.setApi("http://************:8095/svrBankAbutment.asmx/");
		p.setApiAcount("HNYAL");
		p.setApi<PERSON>ey("c4ca4238a0b923820dcc509a6f75849b");
		p.setAddr("浙江省杭州市余杭区凤新路201号");
		p.setDesc("自有产品演示用");
		p.setWxAppId("wx16f06f8165252dc4");
		p.setWxAppSecret("fddabfa695c298b2028ce79a8263c848");
		p.setPayId("**********");
		p.setPayKey("29EjNDFDXXF001JSKl023DLPDUBKDO19");
		p.setSmsSign("智经理");

		p.setPayChannel(ProjectDO.CH_WEIXIN_PAY);
		p.setAllinAppId("********");
		p.setAllinCusId("56333106513RUQ6");
		p.setAllinRsaPrivateKey("MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDIned0Gcjj5x//MmZymv6JZKACdV6F101Cs+54oVBtFLZ6K2A72FOhVHwuM7uG5Eg4dCuZDUYKE9GU6BnHwGW9DUVb0v75oFYZWfEK9jzHwUeCIxDeGf26QNecvYQJXO7Wt6vJ8dxZ5VJUWXL4/k0LqXxUv3D4KtgHAuhg950mTR22PhQN4n9WpxjiyML4LHVtabwVZcyWluFzGpF85IGtH3AZwAJK9+dwjlx6XsYOGM+j21Lwwj9ySkSqiUWA2XgUb/r2WW/pMFVa/qJC6eUXtowwMilQjpHb9jauGo53Ryvj+tGG+2fETpWvXAzHBWEBzoGP79vxhTii/hajufIXAgMBAAECggEAAkEGl7RZIHkOpU8LUvWmLyRVSTxzr8Hyg/Gs7T1gFPRVnzEbkgXlMqaAsALeyYOJGDFInXNGZS3sfeV5sgKEYh0NuXFjx1Q42MISRj2WreaLB3hIuTtw3AXce6NZd2124Cy3c7u8Q7pNiYBpHUkQ09+3SOXPFCAPG00PZVTYNtTujBqeQp0t0o5dFPqy26uSUuO7EXG/lvr8iXeRSBCBZNe/+ji7plhyoveiiTvdjt2KHdOKMp1+kbzpaUl2pQmlVzvkEmZ8ky0K1EzqDX3EdUMs2W00chHheaPzouhUQY1B37hCrWAw4MnaRmo0GcRYdimvnpIPqQdcNDTtRWkRyQKBgQDsENxv8q3xEe2pr2OQfBncyKVtAj4szxNaxE5pEa1yD9gvFjkOu6pmlZNKxVnN3JLqAwF2Df6GbAH/AI54kDpNprp4vE1AAmkLoUYvvtkyPOxzDYxBLiKZxm2iG0EJTVePABPyhw45srZyiOf2G6xCvV6K1uynPPwrev3usAJAswKBgQDZjrm01Fwnc1z253KyahOGhTnV6M2Q7IOT9+MxAba6UIJrjh2nC55I2jDmD/gP7bGX+JU/dDJy8kxGwPgoqoYAhLDETGVQlXDyzhcfSDZ/CqnXkH4V0LjYnNwoCLiqozWxpHaq+dSF649salByds+YAaI4OhvPYvEnusmFluMzDQKBgG7MWyqQFOQZLht+npSqKtlds4c+jkAp0UPA76yRB3+JwEYZgB5SJuCPQ9rMViIxbSM41e67EsO03hr5/+TGjRb1MoCIoRlyCCvVPkQap4bU4YX+zd2p8cOLRtnXQOdEzdvohXRHv32FS+zmJTl2M5v/lehEiD7WeOVPjPAC3IvRAoGBAKRpFvCQN6Bcr1mr6WLphR342o9MYuVtx7Xv9JKZf/TnWNhdSw1tW04lDiUe1lF7SOXXJzsP3fw+HeSuehuJWsFThZ6jOUGf3f6wPosWcJCrh1TvzklANiEE0d+zT5MsyiwYZNGrAa+/J+E/qtIF9bYjLeueBlxAooxqvVamg8aVAoGBAOQVy1zXC/dqB4hSDvzPQTlBAFT7OyrO6O9C8k2EBg4apWEdNlgpI2GhvMHCYTZFPWpucuuLmVnClukRlsPzGiWXI3525SalhGmwrqNPkScxmKBiz4cU4gkm/cbo8kHqhmYW8EMDeiBkplSzUfOy3f4BvjCvHA4dyVxJ6wFTQQO+");
		p.setAllinRsaPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyJ3ndBnI4+cf/zJmcpr+iWSgAnVehddNQrPueKFQbRS2eitgO9hToVR8LjO7huRIOHQrmQ1GChPRlOgZx8BlvQ1FW9L++aBWGVnxCvY8x8FHgiMQ3hn9ukDXnL2ECVzu1reryfHcWeVSVFly+P5NC6l8VL9w+CrYBwLoYPedJk0dtj4UDeJ/VqcY4sjC+Cx1bWm8FWXMlpbhcxqRfOSBrR9wGcACSvfncI5cel7GDhjPo9tS8MI/ckpEqolFgNl4FG/69llv6TBVWv6iQunlF7aMMDIpUI6R2/Y2rhqOd0cr4/rRhvtnxE6Vr1wMxwVhAc6Bj+/b8YU4ov4Wo7nyFwIDAQAB");
		p.setAllinSignType(ProjectDO.SIGN_TYPE_RSA);
		p.setAllinSM2PrivateKey("MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgtxETTsugyJTK70uA3JtGvYH074kT7f4uRQhK7Ju1XCmgCgYIKoEcz1UBgi2hRANCAATlBFg1aMYHws5ymfGQndv2QlOkCbMNPawZEXPhqjw7RQDdcnfOok+58qWP6ucocqJ50i2y1wG89hhrNcTQnHHe");
		p.setAllinSM2PublicKey("MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE5QRYNWjGB8LOcpnxkJ3b9kJTpAmzDT2sGRFz4ao8O0UA3XJ3zqJPufKlj+rnKHKiedItstcBvPYYazXE0Jxx3g==");



		ProjectDO p2 = new ProjectDO();
		p2.setId(2L);
		p2.setClientKey("FMt837iDeo0Ze0xff25");
		p2.setClientSecret("0ff96de89832941edff01bxcz0a076f15");
		p2.setName("金梧桐小指尖");
		p2.setApi("http://************:8097/svrBankAbutment.asmx/");
		p2.setApiAcount("HNPGC");
		p2.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p2.setAddr("义乌高层次人才创业园");
		p2.setDesc("义乌小商品城商品，第一个线上生产环境");
		p2.setWxAppId("wxee31d452c3e982c7");
		p2.setWxAppSecret("bc104abeba973727fbd665704f24394a");
		p2.setPayId("**********");
		p2.setPayKey("w4t45ygew34r678gdgkr5yk4pog5ko45");
		p2.setSmsSign("智经理");

		
		ProjectDO p3 = new ProjectDO();
		p3.setId(3L);
		p3.setClientKey("NXun810iXEo0Ze0xf015");
		p3.setClientSecret("c8ab958fb92c77174542f81398a82482");
		p3.setName("熊谷智慧用电");//南浔小指尖项目，只查用电量
		p3.setApi("http://************:8100/svrBankAbutment.asmx/");
		p3.setApiAcount("NXJCC");
		p3.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p3.setAddr("南浔");
		p3.setDesc("南浔小程序，按日查询每日用电量，按月查汇总用电量");
		p3.setWxAppId("wx37f1e027b8489d77");
		p3.setWxAppSecret("04d6b1d4272583a1e84df84f0f61004d");
		
		ProjectDO p4 = new ProjectDO();
		p4.setId(4L);
		p4.setClientKey("DMt897iDeo0Ze0xff45");
		p4.setClientSecret("0ff96de89332941edff01nxcz0a076f15");
		p4.setName("余勋小指尖");
		p4.setApi("http://************:8098/svrBankAbutment.asmx/");
		p4.setApiAcount("HZYX");
		p4.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p4.setAddr("余杭余勋园区3号楼");
		p4.setDesc("余杭余勋园区3号楼项目");
		p4.setWxAppId("wx15a23a0f7a075daf");
		p4.setWxAppSecret("2414910c14e3196e02ba3706218df279");
		p4.setPayId("**********");
		p4.setPayKey("Yulihang159681228281596812282828");
		p4.setSmsSign("智经理");
		
		ProjectDO p5 = new ProjectDO();
		p5.setId(5L);
		p5.setClientKey("YAli97iDeo0Ze0xff17");
		p5.setClientSecret("f7b32107df91871ec64506362fff94c2");
		p5.setName("元安里小指尖");
		p5.setApi("http://************:8096/svrBankAbutment.asmx/");
		p5.setApiAcount("HNYAL");
		p5.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p5.setAddr("元安里时尚产业园");
		p5.setDesc("元安里时尚产业园");
		p5.setWxAppId("wxdd3bc16abbc834dd");
		p5.setWxAppSecret("7d773a008874f8ba253c3316e8246231");
		p5.setPayId("**********");
		p5.setPayKey("Yal15975312345678912345678912345");
		p5.setSmsSign("智经理");

		ProjectDO p6 = new ProjectDO();
		p6.setId(6L);
		p6.setClientKey("ZShun24iDeo0Ze0xff16");
		p6.setClientSecret("z7b32107df98161ecff506362fff94cs");
		p6.setName("中顺小指尖");
		//p6.setApi("http://zhongshun.benzhitech.cn/svrBankAbutment.asmx/");
		//中顺部署在内网，采用了内网穿透访问8283--->81
		p6.setApi("http://app.benzhitech.com:8283/svrBankAbutment.asmx/");
		p6.setApiAcount("WX");
		p6.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p6.setAddr("杭州中顺大厦");
		p6.setDesc("杭州中顺大厦项目");
		p6.setWxAppId("wx06527962af7e4725");
		p6.setWxAppSecret("570805fdcc0bbe7b0a1faae41be0e957");
		p6.setSmsSign("智经理");

		p6.setPayChannel(ProjectDO.CH_ALLIN_PAY);
		p6.setAllinAppId("********");
		p6.setAllinCusId("56133106513QDXN");
		p6.setAllinRsaPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCmKoIMHsM7hlxfoCoQ3XGTDgU58na/JM2vnq9MgJpsAgzRITjEUqbzC58KWqDzJcIYf/Bswrlcijfy6hZTukL1c8ODW7wai2KAcNJgb7QaTO3YzGiSufk9bmB1nP7AuqdAUzz/MwMlF2lNzldGh6cjmpVplY9EGtTfQngjOk4NRg0Yy3aK8JBBsr+xf+/fo7Qj6ztsO7BDcKlu53UGBDN7If2ma0EZql/1hfPUsm7ecoeEVA1su/2eTjwRhcCFzDpBEgNfxfvEXVFW9YqtMdmX/SlqBXGvOrx3QdU4jErrflaeUB+F+8HPHE/1yObQsgQZfAcAjoLQQMeD4kGcCz2BAgMBAAECggEASNJxSwGCW0hvnELznI+H8kQfOz2NGAj3kaNQPQ1RX8toNZDDMZrtaq+ppB6C0fy2uwj645dVJ/61NWKspUYIiUySHzPpVJLIePS45iWePzZ+uFgg7kjNt8PzRygmJ23F1bWFEmyXUL6ZWk13R0Q1Fi/tkifG8Th3GV8min3Hbh4TM6paOIpflGCywus2faW0x+e8tC2zKjwMR53M+ItQ4XZEMKTW2FyvDNEmqYOehRzviALXhQmBW/gzeQqbQRiN7L7W5jBLeQKUDXMIU5/N5uqY/piXLcH394iA6CfL8sxrdLEXnTIfo87ZairlddSea7+m+c6b/pwQHvoMHwvHRwKBgQDg4T+FYvQ12QhKJQMULHtlANT0R1qCy4KLJczG87CIeOJQpySbGTt1muffcfxXxdHNhWc079Uc9WG70e905NCrAwk7lpqq99cZj9pDPEu14L37kRbSQML8QyCoFG3+UyYxTm5eBYWsyRGgX5bm4tW0+oQTMjZID4nelPVjLqWx3wKBgQC9KTkBf91MbkUPwlmu4ZIz7bb34Bs4voZuXVYgK66p0iN1sRElrB5mI+gWAsIj+mdULTCwJd1JtNcYsoNSMjBkao/s3m4MFHwDAKddd/K5ha0Wni8/9HDGM8KM0FQCVYTWsTsxRmcy1ZgfbLp5CPXdiXi29MJ8PrKG6ie8L768nwKBgQDCdua4UT7/xcOeYm1MJ785M8PzzjReAODNtY1kylzxVx/WQQOtv98kLyWum2JtAJsST/dU1sUC+steYQfinPA5wjFcvzrW9+WhrD1BgUWzbTq87dW/UdOL0/zMfIY1tO/FGZ7lXX8gUOwWNDNJTAUkMLK/6t7358AAo+2jrDYLHwKBgAfUogh+25FjazZ4zC+iJORDbfSa8WWK9ZOyB3OzezqKeW9Q0i3mDygZSm1G1fSZVBe2+zH/j0aS1uit0yPe8gPmKWkb2CBl5LBXIZaL9Z9aekzaZYhXPSfnUqFzqlaiZaphkdq7WGBRWUfLv3htN4hhPX8NaNOt4MQXA2gV7eZvAoGAEJGDW3qd1oLGDsbco8iAdz642izu2stl/3N22UEsyI4ScGAO/XU0DepWbMiZiwn9DxQ42zEIHrdTlmfWVNalTo/TYQYLYT2V2WOZBuSWx+QSu8DJb31i0172dngIzl94jQmA4Fe6WS45+QplVWP3tnaMdjEbNHLrTFojR2Ny0LU=");
		p6.setAllinRsaPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApiqCDB7DO4ZcX6AqEN1xkw4FOfJ2vyTNr56vTICabAIM0SE4xFKm8wufClqg8yXCGH/wbMK5XIo38uoWU7pC9XPDg1u8GotigHDSYG+0Gkzt2Mxokrn5PW5gdZz+wLqnQFM8/zMDJRdpTc5XRoenI5qVaZWPRBrU30J4IzpODUYNGMt2ivCQQbK/sX/v36O0I+s7bDuwQ3Cpbud1BgQzeyH9pmtBGapf9YXz1LJu3nKHhFQNbLv9nk48EYXAhcw6QRIDX8X7xF1RVvWKrTHZl/0pagVxrzq8d0HVOIxK635WnlAfhfvBzxxP9cjm0LIEGXwHAI6C0EDHg+JBnAs9gQIDAQAB");
		p6.setAllinSignType(ProjectDO.SIGN_TYPE_RSA);


		ProjectDO p7 = new ProjectDO();
		p7.setId(7L);
		p7.setClientKey("JianHu12iAu1O0Zf0xff10");
		p7.setClientSecret("sjb32107df24161ecff5O636zfff00cs");
		p7.setName("双江湖小指尖");
		//接口列表：http://************:34103/Ashx/svrBankAbutment.asmx
		p7.setApi("http://************:34103/ashx/svrBankAbutment.asmx/");
		p7.setApiAcount("YWSJH");
		p7.setApiKey("c4ca4238a0b923820dcc509a6f75849b");

		p7.setAddr("双江湖集团产业园");
		p7.setDesc("双江湖集团产业园");
		p7.setWxAppId("wx1b59f934414e6f28");
		p7.setWxAppSecret("31f896686320b915eb0873b35b5bcb12");
		p7.setPayId("**********");
		p7.setPayKey("Aa123456789101112131415161718192");
		p7.setSmsSign("智经理");
		p7.setOpenSms(false);//关闭除验证码外的其他短信发送


		ProjectDO p8 = new ProjectDO();
		p8.setId(8L);
		p8.setClientKey("KmLsw24f81652460Xf111ls");
		p8.setClientSecret("f0f2f96d89a24941edf01bxcz0O076fl0");
		p8.setName("螺蛳湾小指尖");
		//p8.setApi("http://127.0.0.1:8081/Ashx/svrBankAbutment.asmx/");
		p8.setApi("http://km.benzhitech.com:8081/Ashx/svrBankAbutment.asmx/");
		p8.setApiAcount("HNYAL");
		p8.setApiKey("c4ca4238a0b923820dcc509a6f75849b");
		p8.setAllinPayCallBack("https://km.benzhitech.com/api/allin/callback");

		p8.setAddr("云南昆明螺蛳湾国际商贸城");
		p8.setDesc("云南中豪新螺蛳湾商业管理有限公司");
		p8.setWxAppId("wxa0c321c7c996cc7e");
		p8.setWxAppSecret("d1ecd4179668fe5e0f51128a57a4a96b");
		p8.setSmsSign("智经理");
		p8.setOpenSms(false);//关闭除验证码外的其他短信发送
		p8.setShowPrice(true);//显示电价显示和余额转电价

		p8.setPayChannel(ProjectDO.CH_ALLIN_PAY);
		p8.setAllinAppId("********");
		p8.setAllinCusId("564731065130TNM");
		p8.setAllinRsaPrivateKey("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDedE7dIAr8qo/LVFYr8X6fxe8Da63qRblbYzf7DNvSJ/Qkh56apkrNHfROEO3RIW8VK9DpwyTbC3EVzqxmJqmkep7O2BqzXuj0qpqvOfWGsjTwpPRY07XrnatX+YJ/+rtUceTDurD8USqXasnhcWX92YWoEMMgxAeFI1ook4OFW9F41mQPhPFZ1HMSpqkW7jmUvhacWXrtUF24sUutJS50G70AzYg5rxqwf8BuTd7BPkDNpCP75L4MvEEqV6UXpzWLqVfMBC/ENgqsnl2sjC+4kM+xFmsFWDtvMA/K/5/ya+qKdy25nm9tJvEDKSTDQSO1yjOy+QUXjhl/7vwmUxw5AgMBAAECggEACEu/WgBSdJIs9GIykqc0smM/QCoH+s0rRfuVDaiw5R1Dvd3Xu1ChXb/6GZ6zQGneKASXK7PmYsECRb6hlS99gwJ70WmDqRCMgL0A3DEdxU5i9R37taPTPG8SofW5E99/Kggo0JKnPB5P8loOHw6Q79weCkgHajLb98S7SCPZ2JNjwMZGg/DY+CnzleLpCpdHF8Ycezc1PdhpGk83QVpWnXdc1PtOWBPRdtNDQCMYPA7hx772xtoZtqFU6fcOlf2Xv8JV3WNdoI6WSCx6v0BZtfsShFStg/z5HiPHrORj6TGPnekieTYZArCsZ0paHeRrnTZwgG638Ymw7ax2JvRyYQKBgQD08lE+4sV4WWr/71vl62DNAnasCyml9l0yhqkvi8jkNiuML0X7UX5rE8kIHforqYAJ44BQ17H6ORhxGr7Cv4Dzf2D5ZyD9lWiyh6F2YWn6yncPb14uLIsIWREuP+TzmzWTsjYaBqI9uC3EshgCs7jro4O9lYl6dH44sCxWlZ8j2QKBgQDofieuxxlYGhZD5KsYfALFZwdVeq+sNqPzz3/JbWQP2ZGyhYgXp0DeeGZ+xGQxXR2G8qaqb9dQ7ajqFuODf17i9/L0O+HNXHhaLndRoO90CNZnpgyFUA3syWE5up3Nrg+/ZVYRi9RUJ7pWogQz1BykMFBYlOrcVSlg1i3B7eFfYQKBgQCATFf/31ndYsCn06DH+hTW7N5p2OL0uekw6NW/QLF1z2XZ5Hz8WUAlJ9TZepIpwnt9MntoXMItNTC3NXILobESWXrax4jBKjwPnvkUf4u6P8hC/mtPNieQtM5J8k+ixX43oVWzJhxexZS7fSkUaTyHuDrt12COEmx2lthydaZIaQKBgHO2ZjW87kZFJZPA+c8D2bU6JBJNL6X8w/kS6zzrbwPce4FoqGVtXwHa1th9EojnGU0S9BSb+YRT5OAtaCsId8drNMaOD0JQpa3stoIwIT2VMHEB2fV39JZHXFaJJQU7fqb53q5488K2yy1XEdRh26d0TGjqJUJoOGHz924v4R3BAoGAekG0AJM+NCagtfadqKvltw+5ikT5RaUdMqiIcykJirRT659AMTGkfemuqB1N2Ki4eSmoeabUP6qtFcL1brMrnHAxiwdSuuEzLOkwLuHnnNn8CXNQ2vaFFLb4SMh51pBFl8mXdkBMo9vQzvrnldRWfT9obyoz+hjYONFsF7M38wU=");
		p8.setAllinRsaPublicKey("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3nRO3SAK/KqPy1RWK/F+n8XvA2ut6kW5W2M3+wzb0if0JIeemqZKzR30ThDt0SFvFSvQ6cMk2wtxFc6sZiappHqeztgas17o9Kqarzn1hrI08KT0WNO1652rV/mCf/q7VHHkw7qw/FEql2rJ4XFl/dmFqBDDIMQHhSNaKJODhVvReNZkD4TxWdRzEqapFu45lL4WnFl67VBduLFLrSUudBu9AM2IOa8asH/Abk3ewT5AzaQj++S+DLxBKlelF6c1i6lXzAQvxDYKrJ5drIwvuJDPsRZrBVg7bzAPyv+f8mvqinctuZ5vbSbxAykkw0EjtcozsvkFF44Zf+78JlMcOQIDAQAB");
		p8.setAllinSignType(ProjectDO.SIGN_TYPE_RSA);



		map.put(p.getId(),p);
		map.put(p2.getId(),p2);
		map.put(p3.getId(),p3);
		map.put(p4.getId(),p4);
		map.put(p5.getId(),p5);
		map.put(p6.getId(),p6);
		map.put(p7.getId(),p7);
		map.put(p8.getId(),p8);
		
	}
	
	public ProjectDO getProject(Long id){
		return map.get(id);
	}
}
