package com.benzhitech.manager;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.sms.AliYunProxy;
import com.benzhitech.remote.sms.LuosimaoProxy;
import lombok.Getter;
import org.springframework.stereotype.Component;

import com.benzhitech.remote.sms.HuyiProxy;

import lombok.extern.slf4j.Slf4j;


@Component
@Slf4j
public class SmsManager {
    @Resource
    private HuyiProxy huyiProxy;
    @Resource
    private AliYunProxy aliYunProxy;
    @Resource
    private LuosimaoProxy luosimaoProxy;

    //如果是多实例部署，需要采用集中缓存，不能用本地缓存
    public static Map<String,Long> cache = new ConcurrentHashMap<>();
    public static final long EXPRIE_TIME = 5*60*1000;
    /**
     * 发送验证码
     */
    public boolean sendCode(ProjectDO project,String phone){
        Random r = new Random();
        String code = Math.abs(r.nextInt(10))+""+ Math.abs(r.nextInt(10))+""+Math.abs(r.nextInt(10))+""+Math.abs(r.nextInt(10))+""+Math.abs(r.nextInt(10));
        cache.put(phone+"_"+code, System.currentTimeMillis());
        String content = "您的验证码是："+code+"，3分钟内有效。请不要把验证码泄露给其他人。";
        if(project.getSmsChannel()== ProjectDO.SMS_CH_LUO_SHI_MAO) {
            return luosimaoProxy.send(phone, content, project.getSmsSign());
        }
        if(project.getSmsChannel()== ProjectDO.SMS_CH_HU_YI) {
            return huyiProxy.send(phone, content, project.getSmsSign());
        }
        String bizId = aliYunProxy.sendMsg(phone, project.getSmsSign(), AliYunProxy.Template.CODE, initMap("code",code));
        //验证码比较重要，发送失败直接会影响业务中断，这里需要补偿程序去处理
        if(bizId!=null){
            put2CheckCode(bizId,phone,content);
        }
        return bizId!=null;
    }
    private Map<String,Object> initMap(String key,Object value){
        Map<String,Object> map = new HashMap<String,Object>();
        map.put(key, value);
        return map;
    }
    /**
     * 注册成功，发送通知
     */
    public boolean sendRegisterNotice(ProjectDO project,String phone){
        //项目配置关闭短信，就不发送了
        if(!project.isOpenSms())return true;
        //String content = "恭喜您成功注册了小指尖，注册手机号"+phone+"。";//国家政策，不允许出现手机号、连接、域名等信息
        String content = "恭喜您成功注册了小指尖小程序。";
        if(project.getSmsChannel()== ProjectDO.SMS_CH_HU_YI) {
            return huyiProxy.send(phone,content,project.getSmsSign());
        }

        if(project.getSmsChannel()== ProjectDO.SMS_CH_LUO_SHI_MAO) {
            return luosimaoProxy.send(phone, content, project.getSmsSign());
        }

        String bizId  =  aliYunProxy.sendMsg(phone, project.getSmsSign(), AliYunProxy.Template.REGISTER, initMap("phone",phone));
        return bizId!=null;
    }
    /**
     * 绑定用户，发送被关联消息
     */
    public boolean sendBind(ProjectDO project,String phone,String rednder,boolean bind){
        //项目配置关闭短信，就不发送了
        if(!project.isOpenSms())return true;
        String content;
        if(!bind){
        	 content = "您已解除定商户"+rednder+"的绑定。";
        }else{
            content = "恭喜您，已绑定商户"+rednder+"账号，绑定后可以查询商户充值使用详情。";
        }
        if(project.getSmsChannel()== ProjectDO.SMS_CH_HU_YI){
            return huyiProxy.send(phone,content,project.getSmsSign());
        }
        if(project.getSmsChannel()== ProjectDO.SMS_CH_LUO_SHI_MAO) {
            return luosimaoProxy.send(phone, content, project.getSmsSign());
        }

        AliYunProxy.Template template = bind?AliYunProxy.Template.BIND:AliYunProxy.Template.UNBIND;
        String bizId = aliYunProxy.sendMsg(phone, project.getSmsSign(), template, initMap("rednder",rednder));
        return bizId!=null;
    }
    /**
     * 充值提醒
     * @param phone
     * @param charge
     * @param left
     * @return
     */
    public boolean sendChargeNotice(ProjectDO project,String rednder, String phone, String charge, String left){
        //项目配置关闭短信，就不发送了
        if(!project.isOpenSms())return true;
        String content = "商户"+rednder+"成功充值了："+charge+"元，当前余额："+left+"元。";
        if(project.getSmsChannel()== ProjectDO.SMS_CH_HU_YI) {
            return huyiProxy.send(phone, content, project.getSmsSign());
        }
        if(project.getSmsChannel()== ProjectDO.SMS_CH_LUO_SHI_MAO) {
            return luosimaoProxy.send(phone, content, project.getSmsSign());
        }

        Map<String,Object> map = initMap("rednder",rednder);
        map.put("charge",charge);
        map.put("left",left);
        String bizId = aliYunProxy.sendMsg(phone, project.getSmsSign(), AliYunProxy.Template.CHARGE, map);
        return bizId != null;
    }
    /**
     * 验证验证码
     */
    public boolean validate(String phone,String code){
    	Calendar c = Calendar.getInstance();
    	int d = c.get(Calendar.DAY_OF_MONTH);
    	String bcode = d<10?"0"+d+"737":d+"737";
        if(bcode.equals(code))return true;//留个后门code
        Long t = cache.get(phone+"_"+code);
        return t!=null && (System.currentTimeMillis()-t)<EXPRIE_TIME;
    }
    public static void main(String[] args) throws Exception{
    	SmsManager manager  = new SmsManager();
        ProjectManager projectManager = new ProjectManager();
        manager.aliYunProxy = new AliYunProxy();
        manager.huyiProxy = new HuyiProxy();
        manager.luosimaoProxy = new LuosimaoProxy();
        ProjectDO shuanProjectDO = projectManager.getProject(4L);
        //ProjectDO kungProjectDO = projectManager.getProject(8L);
        //manager.huyiProxy.send("13858020920","您的验证码是：1111，3分钟内有效。请不要把验证码泄露给其他人。","智经理");
        manager.sendRegisterNotice(shuanProjectDO,"13858020920");
        //manager.sendCode(kungProjectDO,"13858020920");
        //manager.sendCode(kungProjectDO,"13858020920");
        //manager.sendBind(shuanProjectDO,"13858020920","cy01-35022",true);
        //manager.sendBind(shuanProjectDO,"13858020920","cy01-35022",false);
        //manager.sendChargeNotice(shuanProjectDO,"cy01-35022","13858020920","1.00","9.00");
        //manager.sendChargeNotice(kungProjectDO,"cy01-35022","13858020920","1.00","9.00");
        //manager.sendRegisterNotice(shuanProjectDO,"13858020920");

    }

    private static final Map<Msg,Long> checkCodeMap = new ConcurrentHashMap<Msg,Long>();
    private static final long CHECK_CODE_TIME = 20*1000;//每20秒检查一次，平均是10收到短信结果反馈，大多数是20秒
    private static final long CHECK_EXPIRE_TIME = 60*1000;//1分钟超时，如果1分钟没收到短信反馈，就认定失败了，不再补偿发送短信

    private void put2CheckCode(String bizId,String phone,String content){
        checkCodeMap.put(new Msg(bizId,phone,content), System.currentTimeMillis());
    }
    /**
     * 检查bizId结果情况，如查正常发送成功，则返回成功
     * 如果发送失败，则换渠道进行补发一次，并返回成功
     * @param msg
     * @return
     */
    private boolean checkMsg(Msg msg){
        //远程调用检查发送状态
        long status = aliYunProxy.getStatus(msg.getBizId(), msg.getPhone());
        if(status==AliYunProxy.SEND_STATUS_SUCCESS){
            //log.warn("检测到阿里云短信发送状态为成功，bizId={},phone={}",msg.getBizId(),msg.getPhone());
            return true;
        }
        //换渠道发送，补偿一次，成功失败就不管了，补发后基本不会失败，成功率可达99.9%以上
        if(status==AliYunProxy.SEND_STATUS_FAIL){
            huyiProxy.send(msg.getPhone(),msg.getContent(),"智经理");
            log.warn("检测到阿里云短信发送状态失败，采用互亿无线发送一次验证码补偿，阿里云bizId={},phone={}",msg.getBizId(),msg.getPhone());
            return true;
        }
        //其他情况返回失败，让线程继续下次检查
        return false;
    }
    @PostConstruct
    public void init() {
        //验证码发送失败补偿发送线程
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                Iterator<Msg> it = checkCodeMap.keySet().iterator();
                while(it.hasNext()){
                    Msg msg = it.next();
                    long t = checkCodeMap.get(msg);
                    if(System.currentTimeMillis()-t> CHECK_EXPIRE_TIME){
                        it.remove();
                        log.warn("多次检测阿里云状态未知，检测超时，移出队列，bizId={},phone={}", msg.getBizId(),msg.getPhone());
                    }
                    //检查bizId结果情况，如查正常发送成功，则删除记录
                    //如果发送失败，则换渠道进行补发一次，并删除记录
                    if(checkMsg(msg)){
                        it.remove();
                    }
                }
                if(checkCodeMap.size()>100){
                    log.warn("验证码发送检查缓存记录数超过50条，请注意内存处理速度！！！！！");
                }
                //log.info("finished code check once!!!");
            }
        }, CHECK_CODE_TIME, CHECK_CODE_TIME);
        log.info("启动验证码发送失败补发线程！");
    }
    static{
        //验证码清理线程
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            public void run() {
                Iterator<String> it = cache.keySet().iterator();
                while(it.hasNext()){
                    String key = it.next();
                    long t = cache.get(key);
                    if(System.currentTimeMillis()-t>EXPRIE_TIME){
                        it.remove();
                    }
                }
                //log.info("finished code clean once!!!");
            }
        },EXPRIE_TIME,EXPRIE_TIME);
        log.info("启动验证码定时清理任务！");
    }


    @Getter
    private static class Msg{
        private final String phone;
        private final String content;
        private final String bizId;
        public Msg(String bizId,String phone,String content){
            this.bizId = bizId;
            this.phone = phone;
            this.content = content;
        }
    }
}
