package com.benzhitech.manager;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.ResultCode;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.DesEncoder;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.mapper.LoginMapper;
import com.benzhitech.mapper.UserMapper;
import com.benzhitech.model.UserDO;
import com.benzhitech.remote.RemoteUserProxy;
import com.benzhitech.remote.wx.WeiXinProxy;

@Service
public class UserManager {
	@Resource
	private WeiXinProxy weiXinProxy;
	@Resource
	private UserMapper userMapper;
	@Resource
	private LoginMapper loginMapper;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	@Resource
	private ProjectManager projectManager;
	@Resource
	private SmsManager smsManager;
	
	public boolean isRegister(String phone,String wxId){
		long projectId = ContextHolder.get().getProjectDO().getId();
		return userMapper.getUserByPhoneWxId(phone,wxId,projectId)!=null;
	}
	
	public UserDO getUserByWxId(String wxId){
		long projectId = ContextHolder.get().getProjectDO().getId();
		return userMapper.getUser(wxId,projectId);
	}
	public UserDO getUserById(long userId){
		long projectId = ContextHolder.get().getProjectDO().getId();
		return userMapper.getUserById(userId,projectId);
	}

	public Result<Object> getToken(String wxCode){
		Map<String,String> wxMap = weiXinProxy.getWxSessionAndOpenId(ContextHolder.get().getProjectDO(),wxCode);
		Result<Object> result = new Result<Object>();
		String openId = wxMap.get(WeiXinProxy.WX_KEY_OPENID);
		if(StringUtil.isEmpty(openId) || "null".equals(openId)){
			result.setCode(ResultCode.WX_CODE_ERROR);
			return result;
		}
		long projectId = ContextHolder.get().getProjectDO().getId();
		UserDO user = userMapper.getUser(openId,projectId);
		// 无注册：跳转到注册页面(注册页面重新获取wx_code 给服务端注册，更新登录信息跳转到首页)
		if(user==null){
			//没注册,返回用户微信信息，后面注册会用到
			result.setCode(ResultCode.USER_NOT_REGISTER);
			result.setData(wxMap);
			return result;
		}
		//更新登录信息，返回token,userId,跳转到首页
		String token=generateToken(user.getId(),openId);
		Map<String,Object> mapToken = new HashMap<String,Object>();
		mapToken.put("token", token);
		mapToken.put("openId", openId);
		mapToken.put("userId", user.getId());
		mapToken.put("projectId", user.getProjectId());
		
		result.setSuccess(true);
		result.setData(mapToken);
		BizLog.log(projectId,user.getId(), POINT.USER, ACT.LOGIN, true, user.getPhone(),token,JSON.toJSONString(ContextHolder.get().getPhone()));
		return result;
	}
	private String generateToken(long userId,String wxId){
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.YEAR, 1);
		return DesEncoder.encrypt(userId+"-"+cal.getTime());
	}
	
	public Result<Object> register(UserDO user) {
		Result<Object> result = new Result<Object>();
		if(isRegister(user.getPhone(),user.getWxId())){
			result.setSuccess(false);
			result.setCode(ResultCode.PHONE_HAS_REGISTER);
			return result;
		}

		//注册用户返回的不是UserId，是成功条数
		int num = userMapper.addUser(user);
		if(num==0){
			result.setSuccess(false);
			result.setCode(ResultCode.SYSTEM_ERROR);
			return result;
		}
		
		//发送手机通知
		String sign = ContextHolder.get().getProjectDO().getSmsSign();
		smsManager.sendRegisterNotice(ContextHolder.get().getProjectDO(),user.getPhone());
		//插入登录记录，生成token
		String token = generateToken(user.getId(),user.getWxId());
		
		//返回userId,token
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("userId", user.getId());
		map.put("token", token);
		result.setSuccess(true);
		result.setData(map);
		return result;
	}
}
