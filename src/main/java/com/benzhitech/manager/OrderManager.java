package com.benzhitech.manager;

import java.util.*;

import javax.annotation.Resource;

import com.benzhitech.mapper.TaxTitleMapper;
import com.benzhitech.model.*;
import com.benzhitech.remote.allinpay.AllInPayProxy;
import com.benzhitech.remote.allinpay.util.SybUtil;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.ResultCode;
import com.benzhitech.common.context.ContextHolder;
import com.benzhitech.common.util.BizLog;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.common.util.BizLog.ACT;
import com.benzhitech.common.util.BizLog.POINT;
import com.benzhitech.mapper.OrderMapper;
import com.benzhitech.mapper.UserMapper;
import com.benzhitech.remote.RemoteUserProxy;
import com.benzhitech.remote.wx.WeiXinProxy;
import com.benzhitech.remote.wx.util.PayUtil;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class OrderManager {
	@Resource
	private WeiXinProxy weiXinProxy;
	@Resource
	private AllInPayProxy allInPayProxy;
	@Resource
	private OrderMapper orderMapper;
	@Resource
	private RemoteUserProxy remoteUserProxy;
	@Resource
	private ProjectManager projectManager;
	@Resource
	private UserMapper userMapper;
	@Resource
	private UserBindManager userBindManager;
	@Resource
	private SmsManager smsManager;
	@Resource
	private TaxTitleMapper taxTitleMapper;
	public List<OrderDO> getOrders(String mId){
		Map<String,Object> map = new HashMap<String,Object>();
		//map.put("userId", userId);
		map.put("mId", mId);
		ProjectDO project = ContextHolder.get().getProjectDO();
		map.put("projectId", project.getId());
		List<OrderDO> list  = orderMapper.queryList(map);
		fitInvoiceState(list);
		return list;
	}
	private void fitInvoiceState(List<OrderDO> list){
		if(list==null || list.isEmpty())return;
		for(OrderDO order:list){
			//2025年5月份以后3个月以内的才能开票，超过3个月或者2025年5月份之前的，并且order.getInvoiceState()==Order.INVOICE_STATE_CREATE，统一设置为Order.INVOICE_STATE_EXPIRED(过期)
			checkAndExpireInvoiceState(order);
			//开票状态同步
			if(order.getInvoiceState()!=null && order.getInvoiceState()==OrderDO.INVOICE_STATE_PROCESSING){
				//更新发票状态为成功，并更新发票文件路径
				String file = remoteUserProxy.queryTaxFile(ContextHolder.get().getProjectDO(),order.getId());
				if(!StringUtil.isEmpty(file)){
					OrderDO update = new OrderDO();
					update.setId(order.getId());
					update.setInvoiceFile(file);
					update.setProjectId(order.getProjectId());
					update.setVersion(order.getVersion());
					update.setInvoiceState(OrderDO.INVOICE_STATE_SUCCESS);
					update.setGmtModified(new Date());
					if(orderMapper.update(update)>0){
						order.setInvoiceState(OrderDO.INVOICE_STATE_SUCCESS);
						order.setInvoiceFile(file);
                        //log.info("------------------------更新发票状态:{},file={}", order.getId(), file);
					}
				}
			}
		}
	}
	/**
	 * 判断并更新订单的发票状态是否过期
	 * @param order 订单对象
	 */
	private static final int INVOICE_EXPIRE_MONTHS = 3;
	private static final int YEAR_2025_MAY = 202505; // 表示2025年5月
	private void checkAndExpireInvoiceState(OrderDO order) {
		if (order == null || order.getInvoiceState() ==null || order.getInvoiceState() != OrderDO.INVOICE_STATE_CREATE){
			return; // 只处理状态为 CREATE 的订单
		}

		Date now = new Date();
		Calendar cal = Calendar.getInstance();

		cal.setTime(order.getGmtCreated());
		int createdYearMonth = cal.get(Calendar.YEAR) * 100 + cal.get(Calendar.MONTH) + 1; // 创建年月

		// 判断是否是 2025年5月之前的订单
		boolean isBefore2025May = createdYearMonth < YEAR_2025_MAY;

		// 判断是否超过3个月有效期
		cal.add(Calendar.MONTH, INVOICE_EXPIRE_MONTHS);
		boolean isExpiredByCreateTime = cal.getTime().before(now);

		if (isBefore2025May || isExpiredByCreateTime) {
			order.setInvoiceState(OrderDO.INVOICE_STATE_EXPIRED); // 设置为过期
		}
	}
	/**
	 * 支付预创建订单
	 * @param userId
	 * @param wxId
	 * @param mId
	 * @param amount
	 * @return
	 */
	public Result<Map<String,String>> createOrder(long userId,String wxId,String mId,int amount){
		
		Result<Map<String,String>> result = new Result<Map<String,String>>();
		
		long orderId = IDGenerater.generateOrderId();
		Map<String,String> map = null;
		ProjectDO project = ContextHolder.get().getProjectDO();
		String point = null;
		try{
			if(ProjectDO.CH_WEIXIN_PAY==project.getPayChannel()){
				map = weiXinProxy.getWxOrder(project,wxId, mId, orderId, amount);
				point =ACT.PREPAY_WEIXIN;
			} else if (ProjectDO.CH_ALLIN_PAY==project.getPayChannel()) {
				map = allInPayProxy.getAllInPayOrder(project,wxId, mId, orderId, amount);
				point =ACT.PREPAY_ALLIN;
			}

			String packageStr = map.get("package");
			if(StringUtil.isEmpty(packageStr)) {
				BizLog.log(project.getId(), userId, POINT.PAY, point, false, mId,Integer.toString(amount));
				result.setCode(ResultCode.PAY_CREATE_ERROR);
				return result;
			}
			//创建订单
			UserDO user = userMapper.getUser(wxId,project.getId());
			map.put("orderId", Long.toString(orderId));
			String prepayId = packageStr.replace("prepay_id=", "");
			OrderDO order = new  OrderDO();
			order.setId(orderId);
			order.setWxId(wxId);
			order.setCreator(user.getUserName());
			order.setProjectId(project.getId());
			order.setUserId(userId);
			order.setStatus(OrderDO.STATUS_CREATE);
			order.setInvoiceState(OrderDO.INVOICE_STATE_CREATE);
			order.setFitStatus(0);
			order.setAmount(amount);
			order.setVersion(0);
			order.setPrepayId(prepayId);	
			order.setGmtCreated(new Date());
			order.setGmtModified(new Date());
			order.setMId(mId);
			if(orderMapper.insert(order)<=0){
				result.setCode(ResultCode.SYSTEM_ERROR);
				BizLog.log(project.getId(), userId, POINT.PAY, point, false, mId,Integer.toString(amount));
				log.error("新增订单失败,project="+project.getSmsSign()+",userId="+userId+",mId="+mId+",amount="+amount);
				return result;
			}
			BizLog.log(project.getId(), userId, POINT.PAY, point, true, mId,Integer.toString(amount));
			result.setSuccess(true);
			result.setData(map);

		}catch(Exception e){
			log.error("新增订单异常,project="+project.getSmsSign()+",userId="+userId+",mId="+mId+",amount="+amount,e);
			BizLog.log(project.getId(), userId, POINT.PAY, point, false, mId,Integer.toString(amount));
			result.setCode(ResultCode.PAY_CREATE_ERROR);
		}
		return result;
	}


	private static final String FAILED = "failed";
	private static final String SUCCESS = "success";
	private static final String ALLIN_PAY_OK = "0000";
	/**
	 * 通联支付回调订单更新
	 * @param params
	 * @return
	 */
	public String allInNotify(TreeMap<String,String> params){
		//System.out.println("接收到通联支付通知，入参："+JSON.toJSONString(params));
        log.info("接收到通联支付通知，入参：{}", JSON.toJSONString(params));
		//先验签，保证安全
		if(!validateSign(params)){
			return FAILED;
		}
		String projectId = params.get("trxreserved");
		if(StringUtil.isEmpty(projectId)) {
            log.error("通联支付回调缺少projectId,入参：{}", JSON.toJSONString(params));
			return FAILED;
		}
		ProjectDO project = projectManager.getProject(Long.parseLong(projectId));
		if(project == null){
			log.error("通联支付返回项目还未上线，projectId={}", projectId);
			return FAILED;
		}
		String payStatus = params.get("trxstatus");
		if(!ALLIN_PAY_OK.equals(payStatus)){
			log.warn("通联支付付款状态异常，入参：{}", JSON.toJSONString(params));
			return SUCCESS;
		}

		String fee = params.get("trxamt");
		String orderId = params.get("cusorderid");
		String payId = params.get("trxid");

		return updateOrder(ProjectDO.CH_ALLIN_PAY,project,Long.parseLong(orderId),payId,Long.parseLong(fee));
	}
	/*
		通联验签
	 */
	private boolean validateSign(TreeMap<String,String> params){
		String signType = params.get("signtype");
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(signType)) {
			appkey =AllInPayProxy.ALLIN_RSA_PUB_KEY;
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(signType)) {
			appkey = AllInPayProxy.ALLIN_SM2_PUB_KEY;
		}else{
			appkey = AllInPayProxy.ALLIN_MD5_APPKEY;
		}
		try {
			if(!SybUtil.validSign(params, appkey, signType)){
				log.error("通联支付回调验签失败，入参：{}", JSON.toJSONString(params));
				return false;
			}
		} catch (Exception e) {
			log.error("通联支付回调验签异常，入参：{}", JSON.toJSONString(params), e);
			return false;
		}
		return true;
	}
	/**
	 * 接收到notify回调，更新订单状态，同步到远程账户
	 * @param input
	 * @return
	 */
	
	public String weiXinNotify(String input){
		Map<String, String> map;
		Map<String,String> payMap = null;
		try {
			//获取对应的项目支付配置信息
			payMap = PayUtil.doXMLParse(input);
            log.info("接收到微信支付通知，入参：{}", JSON.toJSONString(payMap));
		} catch (Exception e) {
			log.error("微信支付返回内容解析失败,入参：{}",input,e);
			return failed(ProjectDO.CH_WEIXIN_PAY,0,0,"微信支付返回内容解析失败",input);
		}
		//project参数检查
        String projectId = payMap.get("attach");
        if(StringUtil.isEmpty(projectId)) {
        	return failed(ProjectDO.CH_WEIXIN_PAY,0,0,"微信支付回调缺少project_id",payMap);
        }

		ProjectDO project = projectManager.getProject(Long.parseLong(projectId));
		if(project == null){
            log.error("微信支付返回项目还未上线，projectId={}", projectId);
			return failed(ProjectDO.CH_WEIXIN_PAY,0,0,"微信支付project_id对应项目未上线",payMap);
		}
		//微信支付状态检查
		map = weiXinProxy.notify(project,payMap);
		if(map==null) {
            log.error("微信支付签名检查失败或微信端未成功支付,入参：{}", JSON.toJSONString(payMap));
        	return failed(ProjectDO.CH_WEIXIN_PAY,project.getId(),0,"签名检查失败或微信端未成功支付",payMap);
		}
		
		String fee = map.get("total_fee");
		String orderId = map.get("order_id");
		String payId = map.get("wx_trade_no");
		return updateOrder(ProjectDO.CH_WEIXIN_PAY,project,Long.parseLong(orderId),payId,Long.parseLong(fee));

	}
	private String updateOrder(int payChannel,ProjectDO project,long orderId,String payId,long fee){
		//订单检查
		OrderDO order = orderMapper.queryById(orderId,project.getId());
		//最后做个幕等检查，如果后续业务处理没那么快，微信支付可能会在一定时间后又发来消息，就出现重复消息理了

		if(order == null || fee!= order.getAmount().longValue()){
			return failed(payChannel,project.getId(),order==null?0:order.getUserId(),"本地订单不存在或支付金额不一致",JSON.toJSONString(order));
		}
		if(order.getStatus()==OrderDO.STATUS_OVER){
			log.warn("订单状态已处理完，收到微信重复回调消息，丢弃后续业务处理{}",JSON.toJSONString(order));
			return success(payChannel,project.getId(),order.getUserId(),order);
		}
		//更新订单状态
		OrderDO update = new OrderDO();
		update.setId(order.getId());
		update.setStatus(OrderDO.STATUS_PAYED);
		order.setStatus(OrderDO.STATUS_PAYED);
		update.setWxTradeNo(payId);
		update.setGmtModified(new Date());
		update.setProjectId(project.getId());

		// 以下两步需要事务处理，数据量不大时，暂时不优化
		if(orderMapper.update(update)<=0) {
			log.error("更新本地数据库订单状态失败{}", JSON.toJSONString(order));
			return failed(payChannel,project.getId(),order.getUserId(),"更新本地数据库订单状态失败",JSON.toJSONString(order));
		}
		//同步账户信息， 失败的话异步程序补偿处理非正常订单
		int fitStatus = remoteUserProxy.charge(project, order.getMId(), order.getId(), order.getAmount());
		BizLog.log(project.getId(), order.getUserId(), POINT.PAY, ACT.SYNC, fitStatus==OrderDO.FIT_STATUS_OK, fitStatus+"",JSON.toJSONString(order));
		update = new OrderDO();
		update.setId(order.getId());
		update.setFitStatus(fitStatus);
		order.setFitStatus(fitStatus);
		update.setGmtModified(new Date());
		update.setProjectId(project.getId());
		//同步成功了，订单周期结束
		if(fitStatus == OrderDO.FIT_STATUS_OK){
			update.setStatus(OrderDO.STATUS_OVER);
			order.setStatus(OrderDO.STATUS_OVER);
		}
		//发送短信通知
		if(orderMapper.update(update)>0 && fitStatus == OrderDO.FIT_STATUS_OK){
			float amount = remoteUserProxy.getAmount(project, order.getMId());
			//这里绑定记录里的是户主手机号，但是户主可能会换，手机号会变，改成谁操作充值谁收到短信
			//UserBindDO ub = userBindManager.getUserBind(order.getUserId(), order.getProjectId(), order.getMId());
			//smsManager.sendChargeNotice(ub.getRender()==null?ub.getMId():ub.getRender(),ub.getPhone(), StringUtil.changeF2Y(order.getAmount()), Float.toString(amount),project.getSmsSign());
			UserDO user = userMapper.getUserById(order.getUserId(),order.getProjectId());
			//短信发送
			smsManager.sendChargeNotice(project,order.getMId(), user.getPhone(), StringUtil.changeF2Y(order.getAmount()), Float.toString(amount));

		}

		return success(payChannel,project.getId(),order.getUserId(),order);
	}
	private static String success(int payChannel,long projectId,long userId,OrderDO order){
		if (ProjectDO.CH_WEIXIN_PAY == payChannel) {
			Map<String, String> map = new HashMap<String,String>();
			map.put("return_code", "SUCCESS");
			map.put("return_msg", "OK");
			BizLog.log(projectId, userId, POINT.PAY, ACT.NOTIFY_WEIXIN, true, JSON.toJSONString(order));
			return StringUtil.map2XML(map);
		}else{
			BizLog.log(projectId, userId, POINT.PAY, ACT.NOTIFY_ALLIN, true, JSON.toJSONString(order));
			return SUCCESS;
		}
	}
	private static String failed(int payChannel,long projectId,long userId,String reason,Map<String, String> payMap){
		return failed(payChannel,projectId,userId,reason,JSON.toJSONString(payMap));
	}
	private static String failed(int payChannel,long projectId,long userId,String ...s){
		if (ProjectDO.CH_WEIXIN_PAY == payChannel) {
			Map<String, String> map = new HashMap<String, String>();
			map.put("return_code", "FAIL");
			map.put("return_msg", "处理失败");
			BizLog.log(projectId, userId, POINT.PAY, ACT.NOTIFY_WEIXIN, false, s);
			return StringUtil.map2XML(map);
		}else{
			BizLog.log(projectId, userId, POINT.PAY, ACT.NOTIFY_ALLIN, false, s);
			return FAILED;
		}
	}

	/**
	 * 目前只有元安里在用
	 * @param orderId
	 * @param taxType
	 * @param taxTitleDO
	 * @return
	 */
	public Result<Boolean> openInvoice(String orderId, int taxType, TaxTitleDO taxTitleDO) {
		OrderDO order = orderMapper.queryById(Long.parseLong(orderId),taxTitleDO.getProjectId());
		Result<Boolean> result = new Result<Boolean>();
		//未支付成功的，或者在开票中的 或者已经开过票的，不能开票
		if(order==null || order.getStatus() == OrderDO.STATUS_CREATE
				|| order.getInvoiceState() == OrderDO.INVOICE_STATE_PROCESSING
				|| order.getInvoiceState() == OrderDO.INVOICE_STATE_SUCCESS
				|| (taxType!=OrderDO.TAX_TYPE_VAT_INVOICE && taxType!=OrderDO.TAX_TYPE_COMMON_INVOICE)
		){
			result.setCode(ResultCode.INVOICE_PARAMETER_ILLEGAL);
			return result;
		}

		//检查是不是2025年5月之后3月之内的发票
		checkAndExpireInvoiceState(order);
		if(order.getInvoiceState()==OrderDO.INVOICE_STATE_EXPIRED){
			result.setCode(ResultCode.PARAMETER_ILLEGAL);
			return result;
		}

		//更新订单状态为开票中
		OrderDO update = new OrderDO();
		update.setId(order.getId());
		update.setInvoiceState(OrderDO.INVOICE_STATE_PROCESSING);
		update.setGmtModified(new Date());
		update.setProjectId(taxTitleDO.getProjectId());
		update.setVersion(order.getVersion());
		if(orderMapper.update(update)<=0){
			result.setCode(ResultCode.SYSTEM_ERROR);
			return result;
		}
		//开始开票
		boolean b = remoteUserProxy.openInvoice(ContextHolder.get().getProjectDO(),order.getId(),taxType,taxTitleDO);
        BizLog.log(taxTitleDO.getProjectId(),order.getUserId(),POINT.INVOICE,ACT.OPEN_INVOICE, b,JSON.toJSONString(taxTitleDO));
		if(!b){
			update.setVersion(null);
			update.setInvoiceState(OrderDO.INVOICE_STATE_CREATE);
			orderMapper.update(update);
			result.setCode(ResultCode.INVOICE_PARAMETER_ILLEGAL);
			return result;
		}

		//更新或保存开票信息
		TaxTitleDO olderTaxTitle = taxTitleMapper.queryByUserId(order.getUserId(),taxTitleDO.getProjectId());
		if(olderTaxTitle!=null){
			taxTitleDO.setId(olderTaxTitle.getId());
			taxTitleMapper.updateById(taxTitleDO);
		}else{
			taxTitleDO.setId(IDGenerater.generateOrderId());
			taxTitleMapper.insert(taxTitleDO);
		}
		result.setSuccess(true);
		return result;
	}
}
