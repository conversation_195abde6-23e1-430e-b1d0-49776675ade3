package com.benzhitech.model;

import lombok.Data;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class ProjectDO {
	private Long id;
	/**移动端访问安全码**/
	private String clientKey;
	private String clientSecret;
	/**项目名、地址、标描**/
	private String name;
	private String addr;
	private String desc;

	/** 主站接口 **/
	private String api;
	private String apiAcount;
	private String apiKey;

	
	/** 小程序账号 **/
	private String wxAppId;
	private String wxAppSecret;
	private String wxPayCallBack;

	/** 支付渠道，默认腾讯商户平台 **/
	private Integer payChannel = CH_WEIXIN_PAY;
	/** 腾讯商户平台支付 **/
	public static int CH_WEIXIN_PAY = 0;
	/** 通联支付 **/
	public static int CH_ALLIN_PAY = 1;
	/** 银联支付，暂未实现 **/
	public static int CH_UNION_PAY = 2;

	/** 腾讯商户平台下单配置 **/
	private String payId;
	private String payKey;

	/** 通联支付配置 **/
	private String allinCusId;
	private String allinAppId;
	private String allinPayCallBack;
	private String allinMd5AppKey;
	private String allinSignType;
	public static String SIGN_TYPE_MD5 = "MD5";
	public static String SIGN_TYPE_RSA = "RSA";
	public static String SIGN_TYPE_SM2 = "SM2";
	private String allinRsaPrivateKey;
	private String allinRsaPublicKey;
	private String allinSM2PrivateKey;
	private String allinSM2PublicKey;

	/** 短信签名 **/
	private String smsSign;
	/** 大项目除了必要的验证码，其他短信都关闭，默认开启所有短信**/
	private boolean openSms = true;
	/** 短信发送渠道，默认使用互亿无线 */
	private int smsChannel = SMS_CH_LUO_SHI_MAO;
	public static int SMS_CH_HU_YI = 0;
	public static int SMS_CH_ALI_YUN = 1;
	public static int SMS_CH_LUO_SHI_MAO = 2;

	/** 是否转换余额为电量显示 **/
	private boolean showPrice = false;
		
}
