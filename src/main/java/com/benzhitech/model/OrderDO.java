package com.benzhitech.model;

import java.util.Date;

import lombok.Data;

@Data
public class OrderDO {

	private Long id;
	private Long userId;
	private Long projectId;
	private String mId;
	private String wxId;
	
	private String creator;
	/**
	 * 订单状态,0-创建，1-支付成功
	 */
	private Integer status;
	/**
	 * 订单创建
	 */
	public static int STATUS_CREATE = 0;
	/**
	 * 订单微信支付成功
	 */
	public static int STATUS_PAYED = 1;
	/**
	 * 同步成功
	 */
	public static int STATUS_OVER = 2;

	/**
	 * 同步账户状态
	 */
	private Integer fitStatus;
	/**
	 *  同步账户状态成功
	 */
	public static int FIT_STATUS_OK = 1;

	/*
	 * 订单微信支付回调失败
	 */
//	public static int STATUS_PAY_FAILED = 1;
	
	private Integer amount;
	private String prepayId;//微信预付款ID
	
	private String wxTradeNo;//微信回调内容
	/**
	 * 税单状态,-1(过期无法开票), 0-开票失败 1-未开票(默认) 2-开票中 3-开票成功
	 */
	private Integer invoiceState;
	public static int INVOICE_STATE_EXPIRED = -1;
	public static int INVOICE_STATE_FAILED = 0;
	public static int INVOICE_STATE_CREATE = 1;
	public static int INVOICE_STATE_PROCESSING = 2;
	public static int INVOICE_STATE_SUCCESS = 3;

	/**
	 * 税单类型, 1 增值税专用发票  2 增值税普通发票
	 */
	private Integer taxType;
	public static int TAX_TYPE_VAT_INVOICE = 1;
	public static int TAX_TYPE_COMMON_INVOICE = 2;

	/** 发票文件地址  */
	private String invoiceFile;

	private Integer version;
	
	private Date gmtCreated;
	
	private Date gmtModified;

}
