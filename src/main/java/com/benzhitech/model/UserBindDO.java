package com.benzhitech.model;

import java.util.Date;

import lombok.Data;

@Data
public class UserBindDO {
	private Long id;
	private Long userId;
	private String wxId;
	/**
	 * 商户ID
	 */
	private String mId;
	/**
	 * 项目ID
	 */
	private Long projectId;
	/**
	 * 是否绑定，1-绑定，0-未绑定
	 */
	private Integer bind;
	public static final int BIND_OK = 1;
	public static final int BIND_NO = 0;
	/**
	 * 承租人，有可能不是当前微信用户（绑定别人的电表）
	 */
	private String render;
	/**
	 * 地址(门市号)
	 */
	private String addr;
	/**
	 * 商户电话
	 */
	private String phone;
	
	private Date gmtCreated;
	
	private Date gmtModified;
	
	/**
	 * 余额，需要实时调接口查返回，此处不记入User_Bind表
	 */
	private Float  amount;
	/**
	 * 可用电量，按即时电价转换，此处不记入User_Bind表
	 */
	private Float  degree;
	/**
	 * 当前电量，需要实时调接口查返回，此处不记入User_Bind表
	 */
//	private Float degree;
	/**
	 * 0--欠费，1-正常，此处不记入User_Bind表
	 */
	private int amountStatus;
	/**
	 * 绑定/已绑定
	 */
	private String bindText;
	
	public static final int DELETED = 1;
	/**
	 * 是否删除
	 */
	private Integer deleted;
	
}
