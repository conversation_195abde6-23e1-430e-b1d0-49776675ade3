package com.benzhitech.mapper;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.benzhitech.model.UserDO;

@Mapper
public interface UserMapper {
    UserDO getUser(@Param("wxId") String wxId,@Param("projectId") long projectId);
    
    UserDO getUserById(@Param("userId") long userId, @Param("projectId") long projectId);    
    
    UserDO getUserByPhoneWxId(@Param("phone") String phone, @Param("wxId") String wxId,@Param("projectId") long projectId);
    
    int addUser(UserDO user);
}
