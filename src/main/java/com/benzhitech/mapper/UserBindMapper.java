package com.benzhitech.mapper;


import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.benzhitech.model.UserBindDO;

@Mapper
public interface UserBindMapper {
	/**
	 * 查询微信用户关联的账号，包括绑定和未绑定的
	 * @param userId
	 * @return
	 */
    List<UserBindDO> getUserBinds(@Param("userId") long userId,@Param("projectId") long projectId);
    
    UserBindDO getUserBind(Map<String,Object> map);
    
    int addUserBind(UserBindDO ub);
    
    int updateUserBind(UserBindDO ub);
}
