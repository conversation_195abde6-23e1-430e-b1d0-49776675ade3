package com.benzhitech.mapper;


import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.benzhitech.model.OrderDO;

@Mapper
public interface OrderMapper {
	
    int insert(OrderDO order);
    
    int update(OrderDO order);
    
    OrderDO queryById(@Param("id") long id, @Param("projectId") long projectId);
    /**
     * userId 
     * mId
     * @param map
     * @return
     */
    List<OrderDO> queryList(Map<String,Object> map);
}
