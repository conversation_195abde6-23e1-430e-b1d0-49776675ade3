package com.benzhitech;

import javax.annotation.Resource;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.benzhitech.common.interceptor.RequestHandlerInterceptor;

@Configuration
public class AppConfigurer implements WebMvcConfigurer{
	@Resource
	private RequestHandlerInterceptor requestHandlerInterceptor;
	
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration interceptorRegistration = registry.addInterceptor(requestHandlerInterceptor);
        interceptorRegistration.excludePathPatterns("/error");
        interceptorRegistration.excludePathPatterns("/index");
        interceptorRegistration.excludePathPatterns("/api/weixin/callback");
        interceptorRegistration.excludePathPatterns("/api/allin/callback");
        interceptorRegistration.excludePathPatterns("/api/test/weixin/callback");
        interceptorRegistration.excludePathPatterns("/api/test/allin/callback");
        interceptorRegistration.addPathPatterns("/**");
    }

}
