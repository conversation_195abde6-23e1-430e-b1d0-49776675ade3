package com.benzhitech.common;

import com.alibaba.fastjson.JSON;

public class Result<T> {
	private String code;
	private String msg;
	private boolean success;
	
	public void setCode(ResultCode code){
		this.code=code.getCode();
		this.msg = code.getMsg();
		this.success=false;
	}
	private T data;
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public T getData() {
		return data;
	}
	public void setData(T t) {
		this.data = t;
	}
	
	public String toString(){
		return JSON.toJSONString(this);
	}
}
