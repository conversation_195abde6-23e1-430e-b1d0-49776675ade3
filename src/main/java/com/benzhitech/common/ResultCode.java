package com.benzhitech.common;

import lombok.Getter;
import lombok.Setter;

public enum ResultCode {
	SYSTEM_FORBIDDEN("system_forbidden","禁止非法访问"),
	PARAMETER_ILLEGAL("parameter_illegal","参数非法"),
	PARAMETER_ERROR("parameter_error","参数出错"),
	SYSTEM_ERROR("system_error","系统出错，请稍候再试"),
	WX_CODE_ERROR("wx_not_exsit","微信用户不存在"),
	REMOTE_USER_NOT_EXSIT("remote_user_not_exsit","不存在该商户号"),
	BIND_NOT_EXSIT("bind_not_exsit","不存在关联商户"),
	BIND_NOT_EXSIT_PHONE("bind_not_exsit_phone","商户无手机号，无法绑定"),
	PHONE_CODE_ERROR("phone_code_error","验证码无效"),
	BIND_IS_EXSIT("bind_is_exsit","已存在关联商户"),
	NOT_PHONE_NUMBER("not_phone_number","手机号不正确"),
	PHONE_HAS_REGISTER("phone_has_register","手机号已注册过"),
	USER_NOT_REGISTER("user_not_register","用户未注册"),
	PAY_NUMBER_ERROR("pay_number_error","充值金额不能少于0元"),
	PAY_CREATE_ERROR("pay_create_order_failed","创建订单失败"),
	INVOICE_PARAMETER_ILLEGAL("invoice_parameter_illegal","提交信息有误，请核对后再提交");
	
	
	@Setter
	@Getter
	private String code;
	@Setter
	@Getter
	private String msg;

	ResultCode(String code,String msg){
		this.code=code;
		this.msg=msg;
	}
	
}
