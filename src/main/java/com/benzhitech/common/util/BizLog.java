package com.benzhitech.common.util;

import org.apache.ibatis.reflection.ArrayUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class BizLog {
	private static final Logger logger = LoggerFactory.getLogger(BizLog.class);
	
	/** 业务日志
	 * @param userId 用户
	 * @param point 功能点
	 * @param act 操作
	 * @param success 操作成功/失败
	 * @param strs 操作参数
	 */
	public static void log(long projectId,long userId,String point,String act,boolean success,String... strs) {
		StringBuffer sb = new StringBuffer();
		sb.append("projectId=");
		sb.append(projectId);
		sb.append(",");
		sb.append("userId=");
		sb.append(userId);
		sb.append(",");
		sb.append(point);
		sb.append("-");
		sb.append(act);
		sb.append(",");
		sb.append("success=");
		sb.append(success);
		sb.append(",");
		sb.append("parameters=");
		sb.append(ArrayUtil.toString(strs));
		logger.info(sb.toString());
	}
	
	public static class POINT{
		/**
		 * 功能点-用户
		 */
		public static final String USER = "用户";
		/**
		 * 功能点-充值
		 */
		public static final String PAY = "充值";


		public static final String INVOICE = "票据";
	}
	public static class ACT {
		/**
		 * 行为-注册
		 */
		public static final String REGISTER = "注册";
		/**
		 * 行为-登录
		 */
		public static final String LOGIN = "登录";
		/**
		 * 行为-关联商户
		 */
		public static final String ADDBIND = "关联商户";
		/**
		 * 行为-"绑定商户
		 */
		public static final String BIND = "绑定商户";
		/**
		 * 行为-解绑商户
		 */
		public static final String UNBIND = "解绑商户";
		/**
		 * 行为-解关商户
		 */
		public static final String DELETEBIND ="解关商户";
		

		/**
		 * 行为-下单
		 */
		public static final String PREPAY_WEIXIN = "微信下单";

		public static final String PREPAY_ALLIN = "通联下单";
		/**
		 * 行为-付款
		 */
		public static final String NOTIFY_WEIXIN = "微信付款";
		public static final String NOTIFY_ALLIN = "通联付款";
		/**
		 * 行为-同步后台
		 */
		public static final String SYNC = "同步后台";

		public static final String OPEN_INVOICE = "开票";
		
	}
	
}