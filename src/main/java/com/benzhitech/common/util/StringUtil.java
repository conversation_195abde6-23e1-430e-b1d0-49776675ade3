package com.benzhitech.common.util;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

public class StringUtil {
	
	
	private static char[] ILLEGAL = {'\'','(',')','*','-',',','\\','#','\"','{','}','@',':','/'};
	
	/**
	 * 判断是否合法参数
	 * @param para
	 * @return
	 */
	public static boolean isLegalParameter(String para) {
		if(isEmpty(para))return true;
		for(char c:ILLEGAL) {
			if(para.indexOf(c)>0) {
				return false;
			}
		}
		return true;
	}
	public static final String REGEX_MOBILE = "^((13[0-9])|(14[0,1,3-9])|(15[0-3,5-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-3,5-9]))\\d{8}$";
	public static boolean isPhone(String phone){
		return !isEmpty(phone) && phone.matches(REGEX_MOBILE);
	}
	public static boolean isEmpty(String str){
		return str==null || "".equals(str);
	}
	public static boolean isNumeric(String str){
		if(isEmpty(str))return false;
		
		for (int i = 0; i < str.length(); i++){
			if (!Character.isDigit(str.charAt(i))){
				return false;
			}
		}
		return true;
	}
	
	public static boolean inArray(String[] arr, String str){
		if(arr==null || arr.length==0)return false;
		for(String s:arr){
			if(s==null && str==null)return true;
			if(s!=null && s.equals(str))return true;
		}
		return false;
	}
	public static String filterInfo(String src,int head,int end){
		if(isEmpty(src))return src;
		StringBuffer sb = new StringBuffer();
		sb.append(src.substring(0,head));
		for(int i=0;i<(src.length()-head-end);i++){
			sb.append("*");
		}
		sb.append(src.substring(src.length()-end,src.length()));
		return sb.toString();
	}
	  /**
     * 元转分，确保price保留两位有效数字
     * @return
     */
    public static int changeY2F(double price) {
        DecimalFormat df = new DecimalFormat("#.00");
        price = Double.valueOf(df.format(price));
        int money = (int)(price * 100);
        return money;
    }
    
    /**
     * 分转元，转换为bigDecimal在toString
     * @return
     */
    public static String changeF2Y(int price) {
        return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100)).toString();
    }
	 public static String map2XML(Map<String,String> param){  
         StringBuffer sb = new StringBuffer();  
         sb.append("<xml>");  
         for (Map.Entry<String,String> entry : param.entrySet()) {   
                sb.append("<"+ entry.getKey() +">");  
                sb.append(entry.getValue());  
                sb.append("</"+ entry.getKey() +">");  
        }    
         sb.append("</xml>");  
         return sb.toString();  
     } 
	public static void main(String[] args) {
		System.out.println(isPhone("18058795829"));
	}
	public static String getIpAddr(HttpServletRequest request) {
        String ipAddress;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (LOCALHOST.equals(ipAddress)) {
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    } catch (UnknownHostException e) {
                        e.printStackTrace();
                    }
                    ipAddress = inet.getHostAddress();
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            // "***.***.***.***".length()
            if (ipAddress != null && ipAddress.length() > 15) {
                if (ipAddress.indexOf(SEPARATOR) > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
                }
            }
        } catch (Exception e) {
            ipAddress = "";
        }
        return ipAddress;
    }
    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST = "127.0.0.1";
    private static final String SEPARATOR = ",";
}
