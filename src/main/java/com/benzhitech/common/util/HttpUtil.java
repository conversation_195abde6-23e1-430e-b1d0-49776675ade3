package com.benzhitech.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.http.NameValuePair;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HttpUtil {
	private static final int SUCCESS_CODE = 200;
	public static JSONArray doPostReurnArry(String url, Map<String,String> inputMap){
		String result = post(url,inputMap);
        /**
         * 转换成json,根据合法性返回json或者字符串
         */
        try{
        	return JSONObject.parseArray(result);
        }catch (Exception e){
        	log.error("发送HTTP请求失败，url="+url+",inputMap="+JSON.toJSONString(inputMap),e);
        }
        return null;
	}
	public static JSONObject doPost(String url, Map<String,String> inputMap){
		String result = post(url,inputMap);
        /**
         * 转换成json,根据合法性返回json或者字符串
         */
        try{
            return JSONObject.parseObject(result);
           
        }catch (Exception e){
        	log.error("发送HTTP请求失败，url="+url+",inputMap="+JSON.toJSONString(inputMap),e);
        }
        return null;
	}
	/**
     * 发送POST请求
     * @param url
     * @param nameValuePairList
     * @return JSON或者字符串
     * @throws Exception
     */
    private static String post(String url, Map<String,String> inputMap){
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        try{
            /**
             *  创建一个httpclient对象
             */
            client = HttpClients.createDefault();
            /**
             * 创建一个post对象
             */
            HttpPost post = new HttpPost(url);
            List<NameValuePair> parameters = new ArrayList<NameValuePair>(0);
            for(String key:inputMap.keySet()){
            	 parameters.add(new BasicNameValuePair(key, inputMap.get(key)));
            }
            /**
             * 包装成一个Entity对象
             */
            StringEntity entity = new UrlEncodedFormEntity(parameters, "UTF-8");
            /**
             * 设置请求的内容
             */
            post.setEntity(entity);
            /**
             * 设置请求的报文头部的编码
             */
            post.setHeader(new BasicHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8"));
            /**
             * 设置请求的报文头部的编码
             */
            post.setHeader(new BasicHeader("Accept", "text/plain;charset=utf-8"));
            /**
             * 执行post请求
             */
            response = client.execute(post);
            /**
             * 获取响应码
             */
            int statusCode = response.getStatusLine().getStatusCode();
            if (SUCCESS_CODE == statusCode){
                /**
                 * 通过EntityUitls获取返回内容
                 */
                return EntityUtils.toString(response.getEntity(),"UTF-8");
            }else{
            	log.error("发送HTTP请求失败，url="+url+",inputMap="+JSON.toJSONString(inputMap)+",response="+JSON.toJSONString(response));
            }
        }catch (Exception e){
        	log.error("发送HTTP请求失败，url="+url+",inputMap="+JSON.toJSONString(inputMap),e);
        }finally {
        	try{
            response.close();
            client.close();
        	}catch(Exception e){
        		//negro
        	}
        }
        return "";
    }
	/**
     * post请求
     * @param url
     * @param json
     * @return
     */
    public static JSONObject doPost(String url,JSONObject json){
        
        CloseableHttpClient httpclient = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        JSONObject response = null;
        try {
            StringEntity s = new StringEntity(json.toString());
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");//发送json数据需要设置contentType
            post.setEntity(s);
            HttpResponse res = httpclient.execute(post);
            if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                String result = EntityUtils.toString(res.getEntity());// 返回json格式：
                response = JSONObject.parseObject(result);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return response;
    }
}
