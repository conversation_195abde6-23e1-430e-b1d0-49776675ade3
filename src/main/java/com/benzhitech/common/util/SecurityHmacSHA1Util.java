package com.benzhitech.common.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;


import java.util.Base64;
 
public class SecurityHmacSHA1Util {
	
	 public static String hmacSHA1(String content,String key) {
		 return hmac(content,"UTF-8",key,"HmacSHA1");
	 }
	
    /**
	 * hmac+签名算法 加密
	 * @param content  内容
	 * @param charset  字符编码
	 * @param key	         加密秘钥
	 * @param hamaAlgorithm hamc签名算法名称:例如HmacMD5,HmacSHA1,HmacSHA256 
	 * @return
	 */
	public static String hmac(String content, String charset,String key,String hamaAlgorithm){
		byte[] result = null;  
        try {  
            //根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称    
            SecretKeySpec signinKey = new SecretKeySpec(key.getBytes(), hamaAlgorithm);  
            //生成一个指定 Mac 算法 的 Mac 对象    
            Mac mac = Mac.getInstance(hamaAlgorithm);  
            //用给定密钥初始化 Mac 对象    
            mac.init(signinKey);  
            //完成 Mac 操作     
            byte[] rawHmac;
			rawHmac = mac.doFinal(content.getBytes(charset));
			result = Base64.getEncoder().encode(rawHmac);
  
        } catch (Exception e) {  
            System.err.println(e.getMessage());  
        } 
        if (null != result) {  
            return new String(result);  
        } else {  
            return null;  
        }  
    }
	public static void main(String[] args) {
		String hmacSign2 = hmacSHA1("HNPGCQueryRoomInfoHNPGC02011690531890","c4ca4238a0b923820dcc509a6f75849b");
		System.out.println(hmacSign2);
		
		
	}

}
