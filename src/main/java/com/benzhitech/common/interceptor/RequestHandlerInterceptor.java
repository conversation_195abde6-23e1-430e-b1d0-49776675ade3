package com.benzhitech.common.interceptor;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.Result;
import com.benzhitech.common.util.DesEncoder;
import com.benzhitech.common.util.SecuritySHA1Util;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.manager.ProjectManager;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.common.context.Context;
import com.benzhitech.common.context.Context.Phone;
import com.benzhitech.common.context.ContextHolder;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Component
@Configuration
@ConfigurationProperties(prefix = "api")
@Slf4j
public class RequestHandlerInterceptor implements HandlerInterceptor {
	
	@Resource
	private ProjectManager projectManager;
	//private  long TIME_SAFE = 60 *1000;
	/**
	 * 不用检查token的接口
	 */
	private final String[] NO_CHECK_TOKEN_URI = {
			"/api/getUsage",//南浔小程序，不用登录就查询扣费记录，其他项目不允许查询（业务层控制）
			"/api/callback",//支付回调
			"/api/getToken",//用户等录，获取验证码
			"/api/getPhone",//微信未登录时，获取手机号后台解码
			"/api/getRegisterCode",//获取手机注册验证码
			"/api/getUserBindInfoFilter",//注册时，需要查询用户号用于确定绑定，目前暂时用不上了
			"/api/register"//用户注册
			};
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
        String signature = request.getParameter("signature");
        String timestamp = request.getParameter("it");
        String uri = request.getRequestURI();
        //获取项目信息
        if(uri.startsWith("/api/") && !initContext(request,!StringUtil.isEmpty(signature))){
    		log.warn("访问清求缺少projectId参数,uri="+uri+",ip="+StringUtil.getIpAddr(request));
    		return false;
        }
        //检查系统来源是否合法
        if(!checkSignature(uri,signature,timestamp)){
        	writeError(response,createFalseResult("signature.invalid","signature不合法"));
        	log.warn("system-用户非法访问,uri="+uri+",signature="+signature+",timestamp="+timestamp+",ip="+StringUtil.getIpAddr(request));
            return false;
        }
        //检查登录TOKEN是否合法
        if(!StringUtil.inArray(NO_CHECK_TOKEN_URI, uri)){
        	String token=request.getParameter("token");
        	String userId = request.getParameter("user_id");
        	if(StringUtil.isEmpty(token) || StringUtil.isEmpty(userId)) {
        		log.warn("访问清求缺少token或userId参数,uri="+uri+",userId="+userId+",token="+token+"ip="+StringUtil.getIpAddr(request));
        		return false;
        	}
        	String deToken = DesEncoder.decryptor(token);
        	if(!deToken.startsWith(userId)) {
            	writeError(response,createFalseResult("token.invalid","登录token不合法"));
            	log.warn("system-用户TOKE无效,uri="+uri+",userId="+userId+",token="+token+"ip="+StringUtil.getIpAddr(request));
                return false;
        	}
        	//检查token是否失效
        }      
        return true;
    }
	/**
     * 小程序端/外部 请求需要带上系统校验码
    */
    private boolean checkSignature(String uri,String signature,String timestamp) throws Exception{
    	if(StringUtil.isEmpty(uri))return true;
    	if(uri.startsWith("/api/") && !uri.startsWith("/api/admin/")){
    		//没带参数，非法
    		if(StringUtil.isEmpty(signature) || StringUtil.isEmpty(timestamp))return false;
    		ProjectDO proejct = ContextHolder.get().getProjectDO();
    		//以前的三分钟时时间算法比较安全,但是作用于手机用户和服务器同一时区才行
    		String safe = SecuritySHA1Util.shaEncode(proejct.getClientSecret()+"_"+proejct.getClientKey()+"_"+timestamp);
    		return signature.equals(safe);
    	}	
    	return true;
    }
    
    private boolean initContext(HttpServletRequest req,boolean fromPhone) {
    	Context context = new Context();
		if(fromPhone){
			Phone phone = context.new Phone();
			phone.setModel(req.getParameter("device[model]"));
			phone.setBrand(req.getParameter("device[brand]"));
			phone.setSystem(req.getParameter("device[system]"));
			phone.setVersion(req.getParameter("device[version]"));
			phone.setPlatform(req.getParameter("device[platform]"));
			phone.setSdkVersion(req.getParameter("device[sdkVersion]"));
			phone.setIp(StringUtil.getIpAddr(req));
			context.setPhone(phone);
		}
		
    	String projectId = req.getParameter("projectId");
    	if(StringUtil.isNumeric(projectId)) {
    		ProjectDO project = projectManager.getProject(Long.valueOf(projectId));
    		if(project!=null) {
    	   		context.setProjectDO(project);
        		ContextHolder.set(context);
        		return true;
    		}
    	}
    	
    	return false;
    }
    private Result<Boolean> createFalseResult(String code,String msg){
        Result<Boolean> r = new Result<Boolean>();
        r.setCode(code);
        r.setSuccess(false);
        r.setData(false);
        r.setMsg(msg);
        return r;
    }
    public void writeError(HttpServletResponse res,Result<Boolean> r){
    	res.setCharacterEncoding("UTF-8");
    	res.setContentType("text/html; charset=utf-8");
        try {
            res.getOutputStream().write(JSON.toJSONString(r).getBytes("UTF-8"));
        } catch (Exception e) {
        	log.error("API响应异常",e);
        } 
    }
 
    @Override
    public void postHandle(HttpServletRequest req, HttpServletResponse res, Object o, ModelAndView modelAndView) throws Exception {
    }
 
    @Override
    public void afterCompletion(HttpServletRequest req, HttpServletResponse res, Object o, Exception e) throws Exception {
    	ContextHolder.clear();
    }
    public static void main(String[] args) {
    	System.out.println(1561653334850L/(180*1000));//26027555
	}


}
