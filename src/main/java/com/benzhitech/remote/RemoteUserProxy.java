package com.benzhitech.remote;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.benzhitech.model.TaxTitleDO;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.benzhitech.common.util.HttpUtil;
import com.benzhitech.common.util.SecurityHmacSHA1Util;
import com.benzhitech.common.util.StringUtil;
import com.benzhitech.manager.ProjectManager;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.model.MUser;
import com.benzhitech.remote.model.Meter;

import lombok.extern.slf4j.Slf4j;
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;

/**
 * 用户合同信息
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
/**
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class RemoteUserProxy {
	//接口使用说明文档：svrBankAbutment.asmx
	//查询商铺信息
	private static final String GET_USER_INFO = "QueryRoomInfo";
	//查询电表列表
	private static final String GET_USER_DETAIL  = "QueryRoomDetailInfo";
	//查询商户余额
	private static final String GET_AMOUNT_INFO = "QueryOneCardBalance";
	//查询表状态
	private static final String GET_POWER_STATE = "QueryPowerInfo";
	//查询实时电表读数
	private static final String GET_POWER_DEGREE = "QueryRealPowerDegree";
	//查询实时水表读数
	private static final String GET_WATER_DEGREE = "QueryRealWaterDegree";
	//查询电表扣费明细
	private static final String GET_POWER_FEE_DETAIL = "QueryPowerFeeDetail";
	//查询水表扣费明细
	private static final String GET_WATER_FEE_DETAIL = "QueryWaterFeeDetail";
	//查询电表使用量明细
	private static final String GET_POWER_USED_DETAIL = "QueryPowerUseDetail";
	//查询水表使用量明细
	private static final String GET_WATER_USED_DETAIL = "QueryWaterUseDetail";
	//商户充值接口
	private static final String FILL_MONEY = "WXFillMoney";
	//查询电价
	private static final String QUERY_POWER_PRICE = "QueryPowerPrice";

	//开取发票
	private static final String OPEN_INVOICE = "OpenFillTax";
	//查询发票
	private static final String GET_INVOICE_INFO = "QueryTaxState";


	private static void testZhongShun() throws Exception{
		RemoteUserProxy r = new RemoteUserProxy();
		ProjectDO project = (new ProjectManager()).getProject(6L);
		String RoomID = "0502";
		//Object out = r.getUserInfo(project, RoomID);
		//Object out = r.getMeters(project, RoomID);
		//Object out = r.getFeeDetail(project,"051620016883",0,null,null);

		//Object out = r.getDegree(project,"051620016883",0);
		//Object out =r.getAmount(project,RoomID);
		Object out = r.getMeterState(project,"051620016883",0);
		//Object out = r.getUsedDetail(project,"051620016883",0);
		//r.charge(project,RoomID,2024083123440511L,1);
		System.out.println(JSON.toJSONString(out));
		System.out.println("运行结束");
	}
	private static void testKunming() throws Exception{
		RemoteUserProxy r = new RemoteUserProxy();
		ProjectDO project = (new ProjectManager()).getProject(8L);
		String RoomID = "33-4061";
		//Object out = r.getUserInfo(project, RoomID);
		//Object out = r.getMeters(project, RoomID);
		Object out = r.getFeeDetail(project,"124110608777",0,null,null);

		//Object out = r.getDegree(project,"051620016883",0);
		//Object out =r.getAmount(project,RoomID);
		//Object out = r.getMeterState(project,"051620016883",0);
		//Object out = r.getUsedDetail(project,"051620016883",0);
		//r.charge(project,RoomID,2024083123440511L,1);
		System.out.println(JSON.toJSONString(out));
	}
	private static void testKunmingPowerPrice() throws Exception{
		RemoteUserProxy r = new RemoteUserProxy();
		ProjectDO project = (new ProjectManager()).getProject(8L);
		Float out = r.getPowerPrice(project);
		System.out.println(out);
	}
	private static void testJiangHuTax() throws Exception{
		RemoteUserProxy r = new RemoteUserProxy();
		ProjectDO project = (new ProjectManager()).getProject(7L);
		TaxTitleDO taxTitleDO = new TaxTitleDO();
		taxTitleDO.setTaxName("杭州本智科技有限公司");
		taxTitleDO.setTaxNo("91330110563046459J");
		taxTitleDO.setEmail("<EMAIL>");
		r.openInvoice(project, 2505081653572253L, 1, taxTitleDO);
		System.out.println(Calendar.getInstance().getTimeInMillis()/1000);
	}
	private static void testJianghuTaxQuery()throws Exception{
		RemoteUserProxy r = new RemoteUserProxy();
		ProjectDO project = (new ProjectManager()).getProject(7L);
		System.out.println(JSON.toJSONString(r.queryTaxFile(project, 2505081653572253L)));
	}
	public static void main(String[] args) throws Exception {
		((LoggerContext) LoggerFactory.getILoggerFactory()).getLogger("org.apache.http").setLevel(Level.ERROR);
		//testKunming();
		//testJiangHuTax();
		testJianghuTaxQuery();
		//testKunmingPowerPrice();
		//RemoteUserProxy r = new RemoteUserProxy();
		//ProjectDO project = (new ProjectManager()).getProject(2L);
		//System.out.println(JSON.toJSONString(r.getUserInfo(project, "0001")));
		//String LoginName ="HZYX";
		//String RoomID = "A-1020";
		//String sekey = "c4ca4238a0b923820dcc509a6f75849b";
		long Times = Calendar.getInstance().getTimeInMillis()/1000;
		//System.out.println(Times);
		//Times =1690531890;
		//String data = project.getApiAcount() + GET_USER_INFO + project.getApiAcount()+ RoomID + Times;
		//System.out.println("data:"+data);
		
		//String Password = SecurityHmacSHA1Util.hmacSHA1(data, project.getApiKey());
		//String Password = "jjQ04xEZWZejwQpzwdnJt8r0XB8=";
		//Map<String,String> map = new HashMap<String,String>();
		//map.put("LoginName", project.getApiAcount());
		//map.put("RoomID", RoomID);
		//map.put("Times", Times+"");
		//map.put("Password", Password);
		//System.out.println(JSON.toJSONString(map));

		//JSONObject out = HttpUtil.doPost(project.getApi() + GET_USER_INFO, map);
		//Object out = r.getUserInfo(project, RoomID);
		///Object out = r.getMeters(project, RoomID);
		//Object out = r.getUsedDetail(project,RoomID,null,Meter.POWER_METER,"2024-07-01","2024-07-31");
		String[] arr = {"A-501" +
				"2-1-402" +
				"A-101" +
				"2-4-402" +
				"A-501"};
		//long start = System.currentTimeMillis();
        //for (String s : arr) {
            //r.getAmount(project, s);
       // }
		//System.out.println("用时："+(System.currentTimeMillis()-start));
		//电表：000000627574 ，水表：01418451
		//Object out = r.getMeters(project, "0205");
		//int out = r.charge(project, "0201", 123123321234L, 1);
		//Object out = r.getFeeDetail(project, "008200801710", 0);
		//Object out = r.getDegree(project, "123040388443", 0);//123040388443
		//Object out = r.getMeterState(project, "000000627574", 0);
		//System.out.println(JSON.toJSONString(out));

	}
	private Map<String,String> iniParameter(String apiCount,String mId){
		Map<String,String> map = new HashMap<String,String>();
		map.put("LoginName", apiCount);
		if(mId!=null) {
			map.put("RoomID", mId);
		}
		return map;
	}
	/**
	 * 对内容进行hmacSha1加密，生成验签内容
	 */
	private String getPsw(ProjectDO project,String content) {
		String data = project.getApiAcount()+content;
		//System.out.println("data:"+data);
		return SecurityHmacSHA1Util.hmacSHA1(data, project.getApiKey());
	}
	public String queryTaxFile(ProjectDO project,long orderId){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  GET_INVOICE_INFO + project.getApiAcount()+orderId+times;
		String psw = getPsw(project,content);
		input.put("FillNO", Long.toString(orderId));
		input.put("times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + GET_INVOICE_INFO, input);
		if(out==null) {
			log.error("查询发票出错1,projectId={},out={}", project.getId(), JSON.toJSONString(out));
			return null;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("查询发票出错2,projectId={},out={}", project.getId(), JSON.toJSONString(out));
			return null;
		}
		JSONObject taxinfo = out.getJSONObject("taxinfo");
		if(taxinfo==null){
			log.error("查询发票出错3,projectId={},out={}", project.getId(), JSON.toJSONString(out));
			return null;
		}
		return taxinfo.getString("InvoiceFile");
	}
	public boolean openInvoice(ProjectDO project,long orderId, int taxType, TaxTitleDO taxTitleDO){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  OPEN_INVOICE + project.getApiAcount()+orderId+taxType+taxTitleDO.getTaxNo()+taxTitleDO.getTaxName()+taxTitleDO.getEmail() +(taxTitleDO.getRemark()==null?"":taxTitleDO.getRemark())+ times;
		String psw = getPsw(project,content);
		input.put("FillNO", Long.toString(orderId));
		input.put("TaxType", Integer.toString(taxType));
		input.put("TaxNo", taxTitleDO.getTaxNo());
		input.put("TaxName", taxTitleDO.getTaxName());
		input.put("Email", taxTitleDO.getEmail());
		input.put("Remark", taxTitleDO.getRemark()==null?"":taxTitleDO.getRemark());
		input.put("times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + OPEN_INVOICE, input);
		//code 0 校验错误 1 正常 2 失败 3异常
		//{"code":"1","success":true,"message":"开票提交成功，请稍等查询开票结果！"}
		//{"code":"2","success":false,"message":"已经开票，请勿再次提交！"}
		//{"code":"3","success":false,"message":"充值编号不存在"}
		// {"code":"0","success":false,"message":""}
		if(out==null) {
            log.error("开取发票出错1,projectId={},out={}", project.getId(), JSON.toJSONString(out));
			return false;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
            log.error("开取发票出错2,projectId={},out={}", project.getId(), JSON.toJSONString(out));
			return false;
		}
		return true;
	}
	public Float getPowerPrice(ProjectDO project){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  QUERY_POWER_PRICE + project.getApiAcount() + times;
		String psw = getPsw(project,content);
		input.put("Times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + QUERY_POWER_PRICE, input);
		log.debug(JSON.toJSONString(out));
		if(out==null) {
			log.error("获取后台电价信息出错1,projectId="+project.getId()+",out="+JSON.toJSONString(out));
			return null;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("获取后台电价信息出错2,projectId="+project.getId()+",out="+JSON.toJSONString(out));
			return null;
		}
		JSONObject list = out.getJSONObject("list");
		if(list!=null){
			return list.getFloatValue("Prices");
		}
		return null;
	}
	/**
	 * 查询租户基本信息
	 * @param project
	 * @param mId
	 * @return
	 */
	public MUser getUserInfo(ProjectDO project,String mId){
		Map<String,String> input = iniParameter(project.getApiAcount(),mId);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  GET_USER_INFO + project.getApiAcount()+ mId + times;
		String psw = getPsw(project,content);
		input.put("Times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + GET_USER_INFO, input);
		log.debug(JSON.toJSONString(out));
		MUser user = null;
		if(out==null) {
			log.error("获取后台商户信息出错1,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return null;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("获取后台商户接口出错2,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return null;
		}
		
		JSONObject u = out.getJSONObject("room");
		if(StringUtil.isEmpty(u.getString("RoomID")))return null;
		user = new MUser();
		user.setRender(u.getString("Render"));
		user.setMId(u.getString("RoomID"));
		user.setPhone(u.getString("Telphone"));
		user.setAddr(u.getString("ShopAddress"));
		//IsFill:true允许充值，false不允许充值
		if(u.getString("IsFill")!=null){
			user.setLocked(!u.getBooleanValue("IsFill"));
		}
		return user;
	}
	/**
	 * 查询电表信息
	 * @param project
	 * @param mId
	 * @return
	 */
	public List<Meter> getMeters(ProjectDO project,String mId){
		List<Meter> list = new ArrayList<Meter>();
		
		Map<String,String> input = iniParameter(project.getApiAcount(),mId);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  GET_USER_DETAIL + project.getApiAcount()+ mId + times;
		String psw = getPsw(project,content);
		input.put("Times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + GET_USER_DETAIL, input);
		log.debug(JSON.toJSONString(out));
		if(out==null) {
			log.error("获取后台商户信息出错1,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return list;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("获取后台商户接口出错2,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return list;
		}
		JSONObject room = out.getJSONObject("room");
		if(room==null)return list;
		
		JSONArray powerArry = room.getJSONArray("PowerList");
		if(powerArry!=null &&powerArry.size()>0) {
			for(Object s: powerArry) {
				String id = ((JSONObject)s).getString("powernum");
				Meter m = new Meter();
				m.setId(id);
				m.setName(((JSONObject)s).getString("powername"));
				m.setType(Meter.POWER_METER);
				m.setDegree(getDegree(project, id, Meter.POWER_METER));
				if("000000627574".equals(id)) {
					m.setState(Meter.STATE_ON);
				}else {
					m.setState(getMeterState(project,id,Meter.POWER_METER));
				}
				
				list.add(m);
			}
		}
		
		JSONArray waterArry = room.getJSONArray("WaterList");
		if(waterArry!=null &&waterArry.size()>0) {
			for(Object s: waterArry) {
				String id = ((JSONObject)s).getString("WaterNum");
				Meter m = new Meter();
				m.setId(id);
				m.setName(((JSONObject)s).getString("WaterName"));
				m.setType(Meter.WATER_METER);
				m.setDegree(getDegree(project, id, Meter.WATER_METER));
				//m.setDegree(13222.07F);
				m.setState(Meter.STATE_ON);//TODO，先写死，后面有带阀门的再修改为查接口
				list.add(m);
			}
		}
		return list;
	}
	/**
	 * 查询租户余额
	 * @param project
	 * @param mId
	 * @return
	 */
	public float getAmount(ProjectDO project,String mId){
		Map<String,String> input = iniParameter(project.getApiAcount(),mId);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String content =  GET_AMOUNT_INFO + project.getApiAcount()+ mId + times;
		String psw = getPsw(project,content);
		input.put("Times", Long.toString(times));
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + GET_AMOUNT_INFO, input);
		if(out==null) {
			log.error("获取后台商户账号余额失败1,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return 0.0F;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("获取后台商户账号余额失败2,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return 0.0F;
		}
		return out.getFloatValue("balance");
	}
	
	public int getMeterState(ProjectDO project,String meterId,int meterType){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		input.put("Times", Long.toString(times));
		String content = null;
		if(meterType==Meter.POWER_METER) {
			content =  GET_POWER_STATE + project.getApiAcount()+ meterId + times;
			input.put("PowerNum", meterId);
		}else if(meterType==Meter.WATER_METER){
			//水表状态先写死为正常，TODO 后面有项目带阀门的水表，再增加状态
			return Meter.STATE_ON;
		}
		String psw = getPsw(project,content);
		input.put("Password",psw);
		input.put("RoomID","");
		
		JSONObject out = null;
		if(meterType==Meter.POWER_METER) {
			out = HttpUtil.doPost(project.getApi() + GET_POWER_STATE, input);
		}
		if(out!=null && out.getBooleanValue("success")) {
			JSONArray jsa = out.getJSONArray("list");
			if(jsa.size()>0) {
				int state = jsa.getJSONObject(0).getIntValue("state");
				return state==11?Meter.STATE_ON:Meter.STATE_OTHER;
			}
		}
		log.error("获取后台商户表状态失败,projectId="+project.getId()+",meterId="+meterId+",out="+JSON.toJSONString(out));
		return 0;
	}
	/**
	 * 查询表读数
	 * @param project
	 * @param meterId 表ID
	 * @param meterType 表类型0-电表，1-水表
	 * @return
	 */
	public float getDegree(ProjectDO project,String meterId,int meterType){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		input.put("Times", Long.toString(times));
		String content = null;
		if(meterType==Meter.POWER_METER) {
			content =  GET_POWER_DEGREE + project.getApiAcount()+ meterId + times;
			input.put("PowerNum", meterId);
		}else {
			content =  GET_WATER_DEGREE + project.getApiAcount()+ meterId + times;
			input.put("WaterNum", meterId);
		}
		String psw = getPsw(project,content);
		input.put("Password",psw);
		input.put("RoomID","");
		
		JSONObject out = null;
		if(meterType==Meter.POWER_METER) {
			out = HttpUtil.doPost(project.getApi() + GET_POWER_DEGREE, input);
		}else if(meterType==Meter.WATER_METER){
			out = HttpUtil.doPost(project.getApi() + GET_WATER_DEGREE, input);
		}
		log.debug(JSON.toJSONString(out));
		if(out!=null && out.getBooleanValue("success")) {
			JSONArray jsa = out.getJSONArray("list");
			if(jsa.size()>0) {
				Float degree = jsa.getJSONObject(0).getFloat("DegreeNum");
				return degree==null?0:degree;
			}
		}
		log.error("获取后台商户表读数失败,projectId="+project.getId()+",meterId="+meterId+",out="+JSON.toJSONString(out));
		return 0.00f;
	}
	/**
	 * 查询表扣费记录
	 * @param project
	 * @param meterId 表ID
	 * @param meterType 表类型0-电表，1-水表
	 * @return
	 */
	public JSONArray getFeeDetail(ProjectDO project,String meterId,int meterType,String start,String stop){
		Map<String,String> input = iniParameter(project.getApiAcount(),null);
		
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
		String begin = start==null?"2023-01-01":start;
		input.put("Begin",begin);
		String end = stop==null?sf.format(new Date()):stop;
		input.put("End",end);
		input.put("Times", Long.toString(times));
		
		String content = null;
		if(meterType==Meter.POWER_METER) {
			content =  GET_POWER_FEE_DETAIL + project.getApiAcount()+ begin+end+meterId + times;
			input.put("PowerNum", meterId);
		}else if(meterType==Meter.WATER_METER) {
			content =  GET_WATER_FEE_DETAIL + project.getApiAcount()+ begin+end+meterId + times;
			input.put("WaterNum", meterId);
		}
		String psw = getPsw(project,content);
		input.put("Password",psw);
		input.put("RoomID","");
		
		JSONObject out = null;
		if(meterType==Meter.POWER_METER) {
			out = HttpUtil.doPost(project.getApi() + GET_POWER_FEE_DETAIL, input);
		}else {
			out = HttpUtil.doPost(project.getApi() + GET_WATER_FEE_DETAIL, input);
		}
		log.debug("查询商户表使用量、扣费记录："+JSON.toJSONString(out));
		if(out!=null && out.getBooleanValue("success")) {
			log.debug(JSON.toJSONString(out));
			JSONObject jb = new JSONObject();
			jb.put("Powernum", "008200801710");
			jb.put("Amount", 228.18F);
			jb.put("money", 191.65F);
			
			JSONArray ja = new JSONArray();
			JSONObject jbs = new JSONObject();
			jbs.put("PowerID", 3228536);
			SimpleDateFormat sif = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			try {
				jbs.put("EndDate", sif.parse("2023-07-02 00:00:00"));
			} catch (ParseException e) {
			}
			jbs.put("UseAmount", 6.31F);
			jbs.put("price", 0.84F);
			jbs.put("money", 5.3F);
			ja.add(jbs);
			jb.put("PowerList", ja);
			JSONArray jreturn = new JSONArray();
			jreturn.add(jb);
			if("008200801711".equals(meterId)) {
				return jreturn;
			}else {
				return out.getJSONArray("list");
			}
			
		}
		log.error("获取后台商户表扣费详情失败,projectId="+project.getId()+",meterId="+meterId+",out="+JSON.toJSONString(out));
		return null;
	}
	/**
	 * 查询表扣费记录
	 * @param project
	 * @param mId
	 * @param meterId 表ID
	 * @param meterType 表类型0-电表，1-水表
	 * @param start
	 * @param stop
	 * @return
	 */
	public JSONArray getUsedDetail(ProjectDO project,String mId,String meterId,int meterType,String start,String stop){
		Map<String,String> input = iniParameter(project.getApiAcount(),mId);

		long times = Calendar.getInstance().getTimeInMillis()/1000;
		SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
		String begin = start==null?"2023-01-01":start;
		input.put("Begin",begin);
		String end = stop==null?sf.format(new Date()):stop;
		input.put("End",end);
		input.put("Times", Long.toString(times));

		String content =  project.getApiAcount()+ begin+end+(mId==null?"":mId)+(meterId==null?"":meterId) + times;
		if(meterType==Meter.POWER_METER) {
			content =  GET_POWER_USED_DETAIL + content;
			input.put("PowerNum", meterId==null?"":meterId);
		}else if(meterType==Meter.WATER_METER) {
			content =  GET_WATER_USED_DETAIL + content;
			input.put("WaterNum", meterId==null?"":meterId);
		}
		log.debug("加密参数："+content);
		String psw = getPsw(project,content);
		log.debug("Password："+psw);
		input.put("Password",psw);
		input.put("RoomID",mId==null?"":mId);
		log.debug("请求参数："+JSON.toJSONString(input));
		JSONObject out = null;
		if(meterType==Meter.POWER_METER) {
			out = HttpUtil.doPost(project.getApi() + GET_POWER_USED_DETAIL, input);
		}else {
			out = HttpUtil.doPost(project.getApi() + GET_WATER_USED_DETAIL, input);
		}
		log.debug("查询商户表使用量记录："+JSON.toJSONString(out));
		if(out!=null && out.getBooleanValue("success")) {
			log.debug(JSON.toJSONString(out));
			JSONObject jb = new JSONObject();
			jb.put("Powernum", "008200801710");
			jb.put("Amount", 228.18F);
			jb.put("money", 191.65F);

			JSONArray ja = new JSONArray();
			JSONObject jbs = new JSONObject();
			jbs.put("PowerID", 3228536);
			SimpleDateFormat sif = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			try {
				jbs.put("EndDate", sif.parse("2023-07-02 00:00:00"));
			} catch (ParseException e) {
			}
			jbs.put("UseAmount", 6.31F);
			jbs.put("price", 0.84F);
			jbs.put("money", 5.3F);
			ja.add(jbs);
			jb.put("PowerList", ja);
			JSONArray jreturn = new JSONArray();
			jreturn.add(jb);
			if("008200801711".equals(meterId)) {
				return jreturn;
			}else {
				return out.getJSONArray("list");
			}

		}
		log.error("获取后台商户表扣费详情失败,projectId="+project.getId()+",meterId="+meterId+",out="+JSON.toJSONString(out));
		return null;
	}
	/**
	 * 账户充值接口
	 * @param project
	 * @param mId
	 * @param tradeNo
	 * @param amount
	 * @return int 1-成功 2-登陆失败 3-商铺不存在 4-异常错误 5-超过充值限额100000，6-重复订单充值
	 */
	public int charge(ProjectDO project,String mId,Long tradeNo,int amount){
		Map<String,String> input = iniParameter(project.getApiAcount(),mId);
		long times = Calendar.getInstance().getTimeInMillis()/1000;
		String money = StringUtil.changeF2Y(amount);
		input.put("FillMoney", money);
		input.put("tradeNo", tradeNo.toString());
		input.put("Times", Long.toString(times));
		
		String content =  FILL_MONEY + project.getApiAcount()+ mId + money+tradeNo.toString()+times;
		String psw = getPsw(project,content);
		
		input.put("Password",psw);
		JSONObject out = HttpUtil.doPost(project.getApi() + FILL_MONEY, input);
		if(out==null) {
			log.error("商户充值出错1,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
			return 0;
		}
		boolean b = out.getBooleanValue("success");
		if(!b) {
			log.error("商户充值失败,projectId="+project.getId()+",mId="+mId+",out="+JSON.toJSONString(out));
		}
		//code 1-成功 2-登陆失败 3-商铺不存在 4-异常错误 5-超过充值限额100000，6-重复订单充值
		return out.getIntValue("code");
	}
}
