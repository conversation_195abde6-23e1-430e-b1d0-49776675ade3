package com.benzhitech.remote.wx;

import java.util.HashMap;
import java.util.Map;


import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.benzhitech.common.LruCache;
import com.benzhitech.manager.ProjectManager;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.wx.util.GetOpenIDUtil;
import com.benzhitech.remote.wx.util.PayUtil;
import com.benzhitech.remote.wx.util.WXPayUtil;

import lombok.extern.slf4j.Slf4j;


@Component
@Slf4j
public class WeiXinProxy {

    public static final String WX_KEY_SESSION = "sessionKey";
    public static final String WX_KEY_OPENID = "openid";
    public static final String WX_KEY_UNIONID = "unionid";
    public static final String WX_PAY_BODY = "商户充值";
    public static final String PAY_NOTIFY_URL = "https://app.benzhitech.com/api/weixin/callback";
    public static final String WX_PAY_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    public static LruCache<String,Map<String,String>> cache = new LruCache<String, Map<String, String>>(32,128);

    public Map<String,String> getWxSessionAndOpenId(ProjectDO project,String wxCode){
        return GetOpenIDUtil.oauth2GetOpenid(project.getWxAppId(), project.getWxAppSecret(), wxCode);
    }
    public static void main(String[] args) throws Exception {
        WeiXinProxy w = new WeiXinProxy();
        ProjectManager p= new ProjectManager();
        Map<String,String> map = w.getWxOrder(p.getProject(5L),"o0UUL7SgusuFRh3nPsGfo3KIa_F4","t-1-1-101",20240801L, 1);
        System.out.println(JSON.toJSONString(map));

    }
    public Map<String,String> getWxOrder(ProjectDO project,String wxId,String mId,long orderId,int fee) throws Exception{
        String payCallBackUrl = project.getWxPayCallBack()==null?PAY_NOTIFY_URL:project.getWxPayCallBack();
        //商品名称
        //String body = "测试商品名称";
        //金额元=paymentPo.getTotal_fee()*100
        //组装参数，用户生成统一下单接口的签名
        String nonceStr = WXPayUtil.generateNonceStr();
        Map<String, String> packageParams = new HashMap<String, String>();
        packageParams.put("appid", project.getWxAppId());
        packageParams.put("mch_id", project.getPayId());
        packageParams.put("nonce_str", nonceStr);
        packageParams.put("body", project.getName()+"-商户("+mId+")-充值");
        packageParams.put("attach", project.getId().toString());//项目号，回调时要知道是哪个项目的支付订单，才能拿到项目支付配置验签
        packageParams.put("out_trade_no", Long.toString(orderId));//商户订单号
        packageParams.put("total_fee", Integer.toString(fee));//支付金额，这边需要转成字符串类型，否则后面的签名会失败
        packageParams.put("notify_url", payCallBackUrl);//支付成功后的回调地址
        packageParams.put("trade_type", "JSAPI");//支付方式
        packageParams.put("openid", wxId);

        String prestr = PayUtil.createLinkString(packageParams); // 把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串 

        //MD5运算生成签名，这里是第一次签名，用于调用统一下单接口
        //微信支付的商户密钥
        String mysign = PayUtil.sign(prestr, project.getPayKey(), "utf-8").toUpperCase();

        //拼接统一下单接口使用的xml数据，要将上一步生成的签名一起拼接进去
        String xml = "<xml>" + "<appid>" + project.getWxAppId() + "</appid>"
                + "<body><![CDATA[" + project.getName()+"-商户("+ mId + ")-充值]]></body>"
                + "<attach>" + project.getId() + "</attach>"
                + "<mch_id>" + project.getPayId() + "</mch_id>"
                + "<nonce_str>" + nonceStr + "</nonce_str>"
                + "<notify_url>" + payCallBackUrl + "</notify_url>"
                + "<openid>" + wxId + "</openid>"
                + "<out_trade_no>" + orderId + "</out_trade_no>"
                /* + "<spbill_create_ip>" + paymentPo.getSpbill_create_ip() + "</spbill_create_ip>" */
                + "<total_fee>" + fee + "</total_fee>"
                + "<trade_type>JSAPI</trade_type>"
                + "<sign>" + mysign + "</sign>"
                + "</xml>";
        //调用统一下单接口，并接受返回的结果
        String res = PayUtil.httpRequest(WX_PAY_URL, "POST", xml);
        // 将解析结果存储在HashMap中 
        Map<String,String> map=null;
        try {
            map = PayUtil.doXMLParse(res);
        }catch(Exception e){
            log.error("支付下单失败,projectId="+project.getId()+",wxId="+wxId+",amount="+fee+",微信响应信息:"+res,e);
            return new HashMap<String,String>();
        }

        String return_code = (String) map.get("return_code");//返回状态码

        Map<String, String> result = new HashMap<String, String>();//返回给小程序端需要的参数
        String prepay_id = null;
        if("SUCCESS".equalsIgnoreCase(return_code)){
            prepay_id = (String) map.get("prepay_id");//返回的预付单信息   
            result.put("nonceStr", nonceStr);
            result.put("package", "prepay_id=" + prepay_id);
            Long timeStamp = System.currentTimeMillis() / 1000;
            result.put("timeStamp", timeStamp + "");//这边要将返回的时间戳转化成字符串，不然小程序端调用wx.requestPayment方法会报签名错误
            //拼接签名需要的参数
            String stringSignTemp = "appId=" + project.getWxAppId() + "&nonceStr=" + nonceStr + "&package=prepay_id=" + prepay_id+ "&signType=MD5&timeStamp=" + timeStamp;
            //再次签名，这个签名用于小程序端调用wx.requesetPayment方法
            String paySign = PayUtil.sign(stringSignTemp, project.getPayKey(), "utf-8").toUpperCase();

            result.put("paySign", paySign);
        }else {
            log.error("支付下单失败，错误码："+return_code+",入参："+prestr);
        }
        result.put("appid", project.getWxAppId());
        return result;

    }
    /**
     * 支付回调
     * @throws InterruptedException
     */
    public Map<String,String> notify(ProjectDO project,Map<String,String> map){
        if(map==null)return null;

        String returnCode = (String) map.get("return_code");
        if("SUCCESS".equalsIgnoreCase(returnCode)){
            //验证签名是否正确
            Map<String, String> validParams = PayUtil.paraFilter(map);  //回调验签时需要去除sign和空值参数
            String validStr = PayUtil.createLinkString(validParams);//把数组所有元素，按照“参数=参数值”的模式用“&”字符拼接成字符串
            String sign = PayUtil.sign(validStr, project.getPayKey(), "utf-8").toUpperCase();//拼装生成服务器端验证的签名
            //根据微信官网的介绍，此处不仅对回调的参数进行验签，还需要对返回的金额与系统订单的金额进行比对等
            if(sign.equals(map.get("sign"))){
                String total_fee =  (String) map.get("total_fee");
//              double v = Double.valueOf(total_fee) / 100;
                String out_trade_no = ((String)map.get("out_trade_no")).split("O")[0];
//              SimpleDateFormat sf  = new SimpleDateFormat("yyyyMMddHHmmss");
//              Date accountTime = sf.parse((String)map.get("time_end"));
//              String totalAmount = String.valueOf(v);
//              String appId = (String)map.get("appid");
                String tradeNo = (String)map.get("transaction_id");
                Map<String,String> re = new HashMap<String,String>();
                re.put("total_fee", total_fee);
                re.put("order_id", out_trade_no);
                re.put("wx_trade_no", tradeNo);
                return re;
            }else {
                log.error("支付通知接收签名验证失败，projectId="+project.getId()+",响应内容:"+JSON.toJSONString(map));
            }
        }
        return null;
    }

}
