package com.benzhitech.remote.wx.util;

import java.security.spec.AlgorithmParameterSpec;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.yaml.snakeyaml.external.biz.base64Coder.Base64Coder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.benzhitech.common.util.StringUtil;

import lombok.extern.slf4j.Slf4j;


/**
 * 微信小程序获取openid
 * <AUTHOR>
 */
@Slf4j
public class GetOpenIDUtil {
    // 网页授权接口
    public final static String WX_API_SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session?grant_type=authorization_code";
    public static Map<String,String> oauth2GetOpenid(String appid,String appSecret,String code) {
    	StringBuffer urlBuffer = new StringBuffer();
    	urlBuffer.append(WX_API_SESSION_URL);
    	urlBuffer.append("&appid="+appid);
    	urlBuffer.append("&secret="+appSecret);
    	urlBuffer.append("&js_code="+code);
        HttpClient client = null;  
        Map<String,String> result =new HashMap<String,String>(); 
        HttpGet httpget =null;
        try {     
            client = HttpClientBuilder.create().build(); 
            httpget = new HttpGet(urlBuffer.toString());  
            ResponseHandler<String> responseHandler = new BasicResponseHandler();  
            String response = client.execute(httpget, responseHandler);
            
            JSONObject OpenidJSONO=JSONObject.parseObject(response);
            String openid =OpenidJSONO.getString("openid");
            String session_key=OpenidJSONO.getString("session_key");
            String unionid=OpenidJSONO.getString("unionid");
            String errcode=OpenidJSONO.getString("errcode");
            String errmsg=OpenidJSONO.getString("errmsg");

            result.put("openid", openid);
            result.put("sessionKey", session_key);
            result.put("unionid", unionid);
            result.put("errcode", errcode);
            result.put("errmsg", errmsg);
            if(!StringUtil.isEmpty(errcode) && !"null".equals(errcode)){
            	log.error("获取微信用户OPENID出错，响应结果："+JSON.toJSONString(OpenidJSONO));
            }
        } catch (Exception e) {
        	log.error("获取微信用户OPENID异常",e);
        } finally {  
            if(httpget!=null){
            	httpget.releaseConnection();
            }
        }  
        return result;  
    }
	public static String decryptPhone(String sessionKey, String iv, String encrypData) {
		byte[] sData =Base64Coder.decode(sessionKey);
		byte[] iData =Base64Coder.decode(iv);
		byte[] eData =Base64Coder.decode(encrypData);
		try {
			return decryptPhone(sData,iData,eData);
		} catch (Exception e) {
			log.error("decryptPhone-error,data:"+encrypData);
			return "";
		}
	} 
	public static String decryptPhone(byte[] key, byte[] iv,byte[] encData) throws Exception{
		AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		SecretKeySpec keySpec = new SecretKeySpec(key,"AES");
		cipher.init(Cipher.DECRYPT_MODE, keySpec,ivSpec);
		return new String(cipher.doFinal(encData),"UTF-8");
	}
}
