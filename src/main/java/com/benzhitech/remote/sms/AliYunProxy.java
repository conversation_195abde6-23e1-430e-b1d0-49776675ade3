package com.benzhitech.remote.sms;

import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AliYunProxy {
    @Getter
    public enum Template{
        CODE("SMS_476445223", "您的验证码是： ${code} ，3分钟内有效。请不要把验证码泄露给其他人。"),
        REGISTER("SMS_476355222", "恭喜您成功注册了小指尖，注册手机号${phone}。"),
        BIND("SMS_476455227","恭喜您，已绑定商户${rednder}账号，绑定后可以查询商户充值使用详情。"),
        UNBIND("SMS_476490227","您已解除定商户${rednder}的绑定。"),
        CHARGE("SMS_476510221","商户${rednder}成功充值了：${charge}元，当前余额：${left}元。");

        private String code;
        private String desc;
        Template(String code, String desc){
            this.code = code;
            this.desc = desc;
        }
    }
    private static final Client client;
    static {
        Config config = new Config();
        //配置 AccessKey ID
        config.setAccessKeyId("LTAI5tFWXUTKag9gtAt2AoyR");
        //配置 AccessKey Secret
        config.setAccessKeySecret("******************************");
        //配置 Endpoint
        config.endpoint = "dysmsapi.aliyuncs.com";
        config.setRegionId("cn-shenzhen");//深圳
        try {
            client = new Client(config);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
        AliYunProxy proxy = new AliYunProxy();
//        Map<String,Object> map = new HashMap<>();
//        map.put("code",123144);
//		proxy.sendMsg("13858020920", "小指尖", Template.CODE, map);
//        Thread.sleep(5000);
//        map = new HashMap<>();
//        map.put("phone","13858020920");
//        proxy.sendMsg("13858020920", "小指尖", Template.REGISTER, map);
//        Thread.sleep(5000);
//        map = new HashMap<>();
//        map.put("rednder","3-303-1");
//        proxy.sendMsg("13858020920", "小指尖", Template.BIND, map);
//        Thread.sleep(5000);
//        proxy.sendMsg("13858020920", "小指尖", Template.UNBIND, map);
//        map.put("charge", 100.00);
//        map.put("left", 200.00);
//        Thread.sleep(5000);
//        proxy.sendMsg("13858020920", "小指尖", Template.CHARGE, map);
        test();
    }
    private static void test() throws Exception {
        String phone = "13858020920";
        String tempplateCode = Template.CODE.getCode();
        String sign = "智经理";
        Map<String,String> map = new HashMap<>();
        map.put("code",123144+"");

        SendSmsRequest sendSmsRequest = new SendSmsRequest().setPhoneNumbers(phone).setSignName(sign).setTemplateCode(tempplateCode).setTemplateParam(JSON.toJSONString(map));

        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
        System.out.println(JSON.toJSONString(sendSmsResponse));

        Thread.sleep(5000);
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        request.setPhoneNumber(phone);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        request.setSendDate(sf.format(new Date()));
        request.setPageSize(10L);
        request.setCurrentPage(1L);
        request.setBizId(sendSmsResponse.body.bizId);
        try {
            QuerySendDetailsResponseBody body = client.querySendDetails(request).getBody();//不需要判断请求网络异常了
            if("OK".equals(body.code)){
                System.out.println(JSON.toJSONString(body.getSmsSendDetailDTOs()));
//                List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> dtos = body.getSmsSendDetailDTOs();
//                dtos.getSmsSendDetailDTO();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    };
    public long getStatus(String bizId, String phone){
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        request.setPhoneNumber(phone);
        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMdd");
        request.setSendDate(sf.format(new Date()));
        request.setPageSize(10L);
        request.setCurrentPage(1L);
        request.setBizId(bizId);
        try {
            QuerySendDetailsResponseBody body = client.querySendDetails(request).getBody();//不需要判断请求网络异常了
            if("OK".equals(body.code)){
                List<QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO> dtos =body.getSmsSendDetailDTOs().getSmsSendDetailDTO();
                if(!dtos.isEmpty()){
                    long status = dtos.get(0).getSendStatus();
                    if(status==SEND_STATUS_FAIL){
                        log.error("阿里云短信发送状态检查结果：失败，bizId={},phone={},code={}",status,phone,dtos.get(0).getErrCode());
                    }
                    //一般值为1/2/3 具体看官方API文档：https://help.aliyun.com/zh/sms/developer-reference/api-dysmsapi-2017-05-25-querysenddetails
                    return status;
                }
            }
        }catch(Exception e){
            log.error("查询阿里云短信状态远程调用失败",e);
        }
        return SEND_STATUS_UNKNOWN;
    }

    /**
     * 阿里云短信发送成功，状态码为3，见官方API说明
     */
    public static final long SEND_STATUS_SUCCESS = 3;
    /**
     * 阿里云短信发送发送失败，具体要查看ErrCode
     */
    public static final long SEND_STATUS_FAIL = 2;
    /**
     * 阿里云短信检查网络请求失败
     */
    public static final long SEND_STATUS_UNKNOWN = 0;

    public String sendMsg(String phone, String sign, Template template, Map<String,Object> params){
        // 构造请求对象，请替换请求参数值
        String param = JSON.toJSONString(params);
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(sign)
                .setTemplateCode(template.getCode())
                .setTemplateParam(param); // TemplateParam为序列化后的JSON字符串
        // 获取响应对象
        SendSmsResponse response = null;
        try {
            response = client.sendSms(sendSmsRequest);
        }catch (Exception e){
            log.error("发送阿里云短信失败",e);
            return null;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("发送阿里云短信");
        sb.append(",phone=").append(phone);
        sb.append(",template=").append(template.getCode());
        sb.append(",param=").append(param);
        sb.append(",traceId=").append(response.headers.get("x-acs-trace-id"));
        if(response.getStatusCode()!=200){
            sb.append(",statusCode=").append(response.getStatusCode());
            sb.append(",success=失败");
            log.error(sb.toString());
            return null;
        }
        sb.append(",requestId=").append(response.body.requestId);
        sb.append(",bizId=").append(response.body.bizId);
        if(!response.body.code.equals("OK")){
            sb.append(",code=").append(response.body.code);
            sb.append(",msg=").append(response.body.message);
            sb.append(",success=失败");
            log.error(sb.toString());
            return null;
        }else{
            sb.append(",success=成功");
            log.info(sb.toString());
            return response.body.bizId;
        }

    }
}
