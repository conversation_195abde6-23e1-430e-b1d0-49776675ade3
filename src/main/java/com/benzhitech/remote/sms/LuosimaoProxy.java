package com.benzhitech.remote.sms;

import com.benzhitech.common.util.StringUtil;
import com.sun.jersey.api.client.Client;
import com.sun.jersey.api.client.ClientResponse;
import com.sun.jersey.api.client.WebResource;
import com.sun.jersey.api.client.filter.HTTPBasicAuthFilter;
import com.sun.jersey.core.util.MultivaluedMapImpl;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.ws.rs.core.MediaType;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LuosimaoProxy {

    private static final String API_KEY = "0a2144aedf61d285285ea907aca81855";
    private static final String API_URL = "http://sms-api.luosimao.com/v1/send.json";

    private Client client = Client.create();
    private WebResource webResource = null;

    public static void main(String[] args) {
        LuosimaoProxy api = new LuosimaoProxy();
        api.send("13858020920", "您的商户电表已被手机号为138的小指尖电费管家用户绑定。","本智科技");
    }

    public boolean send(final String phone,final String content, String sign){
        if(!StringUtil.isPhone(phone)){
            log.warn("无法发送短信，手机号不正确：{},短信内容：{},签名：杭州本智科技", phone, content);
            return false;
        }
        if(StringUtil.isEmpty(content)){
            log.warn("无法发送短信，短信内容为空：{},签名：杭州本智科技", phone);
            return false;
        }
        if(StringUtil.isEmpty(sign)){
            log.warn("无法发送短信，签名为空：{},短信内容：{}", phone, content);
            return false;
        }
        MultivaluedMapImpl formData = new MultivaluedMapImpl();
        formData.add("mobile", phone);
        formData.add("message", content+"【杭州本智科技】");
        ClientResponse response =  webResource.type( MediaType.APPLICATION_FORM_URLENCODED ).post(ClientResponse.class, formData);
        String textEntity = response.getEntity(String.class);
        try {
            JSONObject jsonObj = new JSONObject(textEntity);
            int error_code = jsonObj.getInt("error");
            String error_msg = jsonObj.getString("msg");
            if (error_code == 0) {
                log.info("发送短信成功，phone={},content={},sign={}", phone, content, "杭州本智科技");
            } else {
                log.error("发送短信失败，phone={},content={},sign={},error_code={},error_msg={}", phone, content, "杭州本智科技",error_code,error_msg);
            }
        } catch (JSONException ex) {
            log.error("发送短信异常，phone={},content={},sign={}", phone, content, "杭州本智科技", ex);
        }
        return true;
    }

    public LuosimaoProxy(){
        client.addFilter(new HTTPBasicAuthFilter("api", API_KEY));
        webResource = client.resource(API_URL);
    }
    private String testSend(){
        MultivaluedMapImpl formData = new MultivaluedMapImpl();
        formData.add("mobile", "13858020920");
        formData.add("message", "您已解除定商户AAA的绑定。【本智科技】");
        ClientResponse response =  webResource.type( MediaType.APPLICATION_FORM_URLENCODED ).post(ClientResponse.class, formData);
        String textEntity = response.getEntity(String.class);
        int status = response.getStatus();
        //System.out.print(textEntity);
        //System.out.print(status);
        return textEntity;
    }

}
