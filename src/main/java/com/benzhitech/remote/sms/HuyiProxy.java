package com.benzhitech.remote.sms;


import com.benzhitech.common.util.StringUtil;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class HuyiProxy {
	private static String API = "http://106.ihuyi.cn/webservice/sms.php?method=Submit";
	
	public boolean send(final String phone,final String content, String sign){
		if(!StringUtil.isPhone(phone)){
            log.warn("无法发送短信，手机号不正确：{},短信内容：{},签名：{}", phone, content, sign);
			return false;
		}
		//异步发送，减少外部依懒响应时间
		new Thread(() -> sendSync(phone,content,sign)).start();
		return true;
	}

	private boolean sendSync(String phone,String content,String sign){
		HttpClient client = new HttpClient(); 
		PostMethod method = new PostMethod(API);
		client.getParams().setContentCharset("GBK");
		method.setRequestHeader("ContentType","application/x-www-form-urlencoded;charset=GBK");
		NameValuePair[] data = {//提交短信
			    new NameValuePair("account", "C54577850"), //查看用户名是登录用户中心->验证码短信->产品总览->APIID
			    new NameValuePair("password", "2af8292451fa8d9c273751a945486b11"),  //查看密码请登录用户中心->验证码短信->产品总览->APIKEY
			    new NameValuePair("mobile", phone), 
			    new NameValuePair("content", content),
			    new NameValuePair("sign", sign), 
		};
		method.setRequestBody(data);
		try {
			client.executeMethod(method);
			String SubmitResult =method.getResponseBodyAsString();
			Document doc = DocumentHelper.parseText(SubmitResult);
			Element root = doc.getRootElement();

			String code = root.elementText("code");
			String msg = root.elementText("msg");
			String smsid = root.elementText("smsid");

			StringBuffer sb = new StringBuffer();
			sb.append("发送互亿短信");
			sb.append(",phone="+phone);
			sb.append(",content="+content);
			sb.append(",code="+code);
			sb.append(",msg="+msg);
			sb.append(",smsid="+smsid);
			 if("2".equals(code)){
				sb.append(",success=成功");
				log.info(sb.toString());
				return true;
			}else{
				sb.append(",success=失败");
				log.error(sb.toString());
			}
		} catch (Exception e) {
			log.error("发送短信异常，phone="+phone+",content="+content+",sign="+sign,e);
		}finally{
			method.releaseConnection();
		}		
		return false;
	}
	public static void main(String[] args) {
		HuyiProxy proxy = new HuyiProxy();
		//proxy.send("13858020920", "您的验证码是：123344，3分钟内有效。请不要把验证码泄露给其他人。","金梧桐");
		//System.out.println(StringUtil.isPhone("8615258020920"));
	}
}
