package com.benzhitech.remote.allinpay.test;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.benzhitech.manager.ProjectManager;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.allinpay.AllInPayProxy;
import com.fasterxml.jackson.databind.ObjectMapper;



public class ApiTestV2 {
	public static void main(String[] args) throws Exception{

		testPay();//统一下单，异步类交易
//		testScanPay();//统一扫码，被扫交易
//		testCancel();//撤销
//		testRefund();//退款
//		testQuery();//查询
	}
	
	public static ProjectDO inintProject(){
		ProjectManager manager = new ProjectManager();
		return manager.getProject(6L);
	}
	public static void testScanPay() throws Exception {
		// TODO Auto-generated method stub
		AllInPayProxy service = new AllInPayProxy();
		String reqsn = String.valueOf(System.currentTimeMillis());

		Map<String, String> map = service.scanPay(inintProject(),1, reqsn,  "标题", "备注",  "134775931316089668","","");
		print(map);
	}

	public static void testQuery() throws Exception{
		AllInPayProxy service = new AllInPayProxy();
		Map<String, String> map = service.query(inintProject(),"", "112094120001088317");
		print(map);
	}
	
	public static void testRefund() throws Exception{
		AllInPayProxy service = new AllInPayProxy();
		String reqsn = String.valueOf(System.currentTimeMillis());
		Map<String, String> map = service.refund(inintProject(),1, reqsn, "", "20160712167578.2547");
		print(map);
	}
	
	public static void testCancel() throws Exception{
		AllInPayProxy service = new AllInPayProxy();
		String reqsn = String.valueOf(System.currentTimeMillis());
		Map<String, String> map = service.cancel(inintProject(),1, reqsn, "112094120001088316", "");
		print(map);
	}
	
	public static void testPay() throws Exception{
		AllInPayProxy service = new AllInPayProxy();
		Map<String, String> map = service.pay(inintProject(),"oL0W060mmfPilb2_3chRz7R59Cnw","2-2-2-1",System.currentTimeMillis(),1);
		//String payInfo  = map.get("payinfo");

		print(map);
	}
	
	public static void print(Map<String, String> map){
		System.out.println("返回数据如下:");
		if(map!=null){
			for(String key:map.keySet()){
				System.out.println(key+";"+map.get(key));
			}
		}
	}
	
	
}
