package com.benzhitech.remote.allinpay;

import com.alibaba.fastjson.JSON;
import com.benzhitech.model.ProjectDO;
import com.benzhitech.remote.allinpay.util.HttpConnectionUtil;
import com.benzhitech.remote.allinpay.util.SybUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Component
@Slf4j
public class AllInPayProxy {
	private final static String SYB_ORGID = "";//集团/机构模式下该参数不为空，且appid与key是与次参数对应
	private final static String PAY_TYPE = "W06";//微信小程序
	private final static String VALID_TIME = "5";//订单支付超时时间
	private final static String PAY_NOTIFY_URL = "https://app.benzhitech.com/api/allin/callback";
	private final static String SYB_APIURL = "https://vsp.allinpay.com/apiweb/unitorder";
	/** 通联RSA公钥，用于验签**/
	public final static String ALLIN_RSA_PUB_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCm9OV6zH5DYH/ZnAVYHscEELdCNfNTHGuBv1nYYEY9FrOzE0/4kLl9f7Y9dkWHlc2ocDwbrFSm0Vqz0q2rJPxXUYBCQl5yW3jzuKSXif7q1yOwkFVtJXvuhf5WRy+1X5FOFoMvS7538No0RpnLzmNi3ktmiqmhpcY/1pmt20FHQQIDAQAB";
	/** 通联SM2 公钥，用于验签 **/
	public final static String ALLIN_SM2_PUB_KEY = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEBQicgWm0KAMqhO3bdqMUEDrKQvYg8cCXHhdGwq7CGE6oJDzJ1P/94HpuVdBf1KidmPxr7HOH+0DAnpeCcx9TcQ==";
	/** 通联MD5 KEY，用于验签 **/
	public final static String ALLIN_MD5_APPKEY = "a0ea3fa20dbd7bb4d5abf1d59d63bae8";

	public Map<String,String> getAllInPayOrder(ProjectDO project, String wxId, String mId, long orderId, int fee) throws Exception{
		Map<String,String>  mapAllInPayOrder = pay(project,wxId,mId,orderId,fee);
		Map<String, String> payinfoMap = JSON.parseObject(mapAllInPayOrder.get("payinfo"), Map.class);

		Map<String, String> result = new HashMap<>();//返回给小程序端需要的参数
		String packageStr = payinfoMap.get("package");//返回的预付单信息
		String nonceStr = payinfoMap.get("nonceStr");
		result.put("nonceStr", nonceStr);
		result.put("package", packageStr);
		result.put("timeStamp", payinfoMap.get("timeStamp"));//这边要将返回的时间戳转化成字符串，不然小程序端调用wx.requestPayment方法会报签名错误
		result.put("paySign", payinfoMap.get("paySign"));
		result.put("appid", project.getWxAppId());
		return result;
	}
	/**
	 * 
	 *  trxamt 交易金额，单位分
	 *  reqsn 商户交易单号
	 *  paytype 交易方式，微信小程序值为 W06，对应的acct为用户小程序的openid
	 *  body 商品标题，最多33个字符
	 *  remark 备注信息，最多100字节，禁止出现+，空格，/，?，%，#，&，=这几类特殊符号
	 *  acct 小程序openid(当paytype=W06时）
	 *  validtime 订单有效时间，默认5分钟，单位分钟
	 *  notify_url 交易结果回调地址，不能带参数，https只能默认443端口
	 *  limit_pay 支付限制，仅“no_credit”不支持信用卡
	 *  idno 证值号，实名交易需要填
	 *  truename 姓名，实名交易需要填
	 *  asinfo 分账信息，需要分账时填写，数据结构看官方文档
	 *  sub_appid 微信小程序appid
	 *  goods_tag  单品优惠信息,只对微信支付有效
	 *  chnlstoreid 渠道门店编号，不知道是否可以用于项目编码？//TODO
	 *  subbranch  通联系统门店号
	 *  extendparams 具体见接口文档,拓展参数
	 *  cusip   限云闪付JS支付业务
	 *  fqnum   限支付宝分期业务
	 * @throws Exception
	 */
	public Map<String,String> pay(ProjectDO project, String wxId, String mId, long orderId, int fee) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SYB_APIURL+"/pay");
		http.init();

		String body = project.getName()+"-商户("+mId+")-充值";
		String remark = project.getId().toString();

		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SYB_ORGID)){
			params.put("orgid", SYB_ORGID);
		}
		params.put("cusid", project.getAllinCusId());
		params.put("appid", project.getAllinAppId());
		params.put("version", "11");
		params.put("trxamt", Integer.toString(fee));
		params.put("reqsn", Long.toString(orderId));
		params.put("paytype", PAY_TYPE);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("body", body);
		params.put("remark", remark);
		params.put("validtime", VALID_TIME);
		params.put("acct", wxId);
		String callBack = project.getAllinPayCallBack()==null?PAY_NOTIFY_URL:project.getAllinPayCallBack();
		params.put("notify_url", callBack);
		params.put("limit_pay", "");
		params.put("sub_appid", project.getWxAppId());
		params.put("goods_tag", "");
		params.put("benefitdetail", "");
		params.put("chnlstoreid", "");
		params.put("subbranch", "");
		params.put("extendparams", "");
		params.put("cusip", "");
		params.put("fqnum", "");
		params.put("idno", "");
		params.put("truename", "");
		params.put("asinfo", "");
		params.put("signtype", project.getAllinSignType());
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
			appkey = project.getAllinRsaPrivateKey();
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
			appkey = project.getAllinSM2PrivateKey();
		}else{
			appkey = project.getAllinMd5AppKey();
		}
		params.put("sign", SybUtil.unionSign(params,appkey,project.getAllinSignType()));
		String s = JSON.toJSONString(params);
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		Map<String,String> map = handleResult(project,result);
		return map;
		
	}
	
	public Map<String,String> cancel(ProjectDO project,long trxamt,String reqsn,String oldtrxid,String oldreqsn) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SYB_APIURL+"/cancel");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SYB_ORGID)) {
			params.put("orgid", SYB_ORGID);
		}
		params.put("cusid", project.getAllinCusId());
		params.put("appid", project.getAllinAppId());
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("oldtrxid", oldtrxid);
		params.put("oldreqsn", oldreqsn);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("signtype", project.getAllinSignType());
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
			appkey = project.getAllinRsaPrivateKey();
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
			appkey = project.getAllinSM2PrivateKey();
		}else{
			appkey = project.getAllinMd5AppKey();
		}
		params.put("sign", SybUtil.unionSign(params,appkey,project.getAllinSignType()));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		Map<String,String> map = handleResult(project,result);
		return map;
	}
	
	public Map<String,String> refund(ProjectDO project,long trxamt,String reqsn,String oldtrxid,String oldreqsn) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SYB_APIURL+"/refund");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SYB_ORGID)) {
			params.put("orgid", SYB_ORGID);
		}
		params.put("cusid", project.getAllinCusId());
		params.put("appid", project.getAllinAppId());
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("oldreqsn", oldreqsn);
		params.put("oldtrxid", oldtrxid);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("signtype", project.getAllinSignType());
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
			appkey = project.getAllinRsaPrivateKey();
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
			appkey = project.getAllinSM2PrivateKey();
		}else{
			appkey = project.getAllinMd5AppKey();
		}
		params.put("sign", SybUtil.unionSign(params,appkey,project.getAllinSignType()));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		Map<String,String> map = handleResult(project,result);
		return map;
	}
	
	public Map<String,String> query(ProjectDO project,String reqsn,String trxid) throws Exception{
		HttpConnectionUtil http = new HttpConnectionUtil(SYB_APIURL+"/query");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SYB_ORGID)) {
			params.put("orgid", SYB_ORGID);
		}
		params.put("cusid", project.getAllinCusId());
		params.put("appid", project.getAllinAppId());
		params.put("version", "11");
		params.put("reqsn", reqsn);
		params.put("trxid", trxid);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("signtype", project.getAllinSignType());
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
			appkey = project.getAllinRsaPrivateKey();
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
			appkey = project.getAllinSM2PrivateKey();
		}else{
			appkey = project.getAllinMd5AppKey();
		}
		params.put("sign", SybUtil.unionSign(params,appkey,project.getAllinSignType()));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		Map<String,String> map = handleResult(project,result);
		return map;
	}
	
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Map<String,String> handleResult(ProjectDO project,String result) throws Exception{
		//System.out.println("ret:"+result);
		Map map = SybUtil.json2Obj(result, Map.class);
		if(map == null){
			throw new Exception("返回数据错误");
		}
		if("SUCCESS".equals(map.get("retcode"))){
			TreeMap tmap = new TreeMap();
			tmap.putAll(map);
			String appkey = "";
			if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
				appkey =ALLIN_RSA_PUB_KEY;
			}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
				appkey = ALLIN_SM2_PUB_KEY;
			}else{
				appkey = ALLIN_MD5_APPKEY;
			}
			if(SybUtil.validSign(tmap, appkey, project.getAllinSignType())){
				System.out.println("签名成功");
				return map;
			}else{
				throw new Exception("验证签名失败");
			}
			
		}else{
			throw new Exception(map.get("retmsg").toString());
		}
	}

	public Map<String, String> scanPay(ProjectDO project,long trxamt,String reqsn,String body,String remark,String authcode,String limit_pay,String asinfo) throws Exception{
		// TODO Auto-generated method stub
		HttpConnectionUtil http = new HttpConnectionUtil(SYB_APIURL+"/scanqrpay");
		http.init();
		TreeMap<String,String> params = new TreeMap<String,String>();
		if(!SybUtil.isEmpty(SYB_ORGID)) {
			params.put("orgid", SYB_ORGID);
		}
		params.put("cusid", project.getAllinCusId());
		params.put("appid", project.getAllinAppId());
		params.put("version", "11");
		params.put("trxamt", String.valueOf(trxamt));
		params.put("reqsn", reqsn);
		params.put("randomstr", SybUtil.getValidatecode(8));
		params.put("body", body);
		params.put("remark", remark);
		params.put("authcode", authcode);
		params.put("limit_pay", limit_pay);
		params.put("asinfo", asinfo);
		params.put("signtype", project.getAllinSignType());
		String appkey = "";
		if(ProjectDO.SIGN_TYPE_RSA.equals(project.getAllinSignType())) {
			appkey = project.getAllinRsaPrivateKey();
		}else if(ProjectDO.SIGN_TYPE_SM2.equals(project.getAllinSignType())) {
			appkey = project.getAllinSM2PrivateKey();
		}else{
			appkey = project.getAllinMd5AppKey();
		}
		params.put("sign", SybUtil.unionSign(params,appkey,project.getAllinSignType()));
		byte[] bys = http.postParams(params, true);
		String result = new String(bys,"UTF-8");
		Map<String,String> map = handleResult(project,result);
		return map;
	}
	
	
}
