package com.benzhitech.remote.model;

import lombok.Data;

@Data
public class Meter {
	
	private String id;
	private String name;
	private Float degree;
	//10 开启失败 11  通电 12 正在开启  20 关闭失败  21 关闭 22 正在关闭
	private Integer state;
	private Integer type;
	/**
	 * 电表
	 */
	public static final int POWER_METER =0;
	/**
	 * 水表
	 */
	public static final int WATER_METER =1;
	
	/** 
	 * 正常
	 */
	public static final int STATE_ON = 1;
	/**
	 * 其他状态
	 */
	public static final int STATE_OTHER = 0;
}
