<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benzhitech.mapper.UserMapper">

    <resultMap id="userMap" type="com.benzhitech.model.UserDO">
    	
    	<result property="projectId" column="project_id" javaType="java.lang.Long"></result>
        <result property="id" column="id" javaType="java.lang.Long"></result>
        <result property="userName" column="user_name" javaType="java.lang.String"></result>
        <result property="wxName" column="wx_name" javaType="java.lang.String"></result>
        <result property="wxId" column="wx_Id" javaType="java.lang.String"></result>
        <result property="phone" column="phone" javaType="java.lang.String"></result>
        <result property="wxSession" column="wx_session" javaType="java.lang.String"></result>
        <result property="gmtCreated" column="gmt_created" javaType="java.util.Date"></result>
        <result property="gmtModified" column="gmt_modified" javaType="java.util.Date"></result>
    </resultMap>

    <select id="getUser" resultMap="userMap">
        select * from fee_user where wx_id = #{wxId} and project_id = #{projectId}
    </select>
    <select id="getUserById" resultMap="userMap" parameterType="java.lang.Long">
        select * from fee_user where id = #{userId} and project_id = #{projectId}
    </select>
    <select id="getUserByPhoneWxId" resultMap="userMap">
        select * from fee_user where phone = #{phone} and wx_id = #{wxId} and project_id = #{projectId}
    </select>
    
    <insert id="addUser" parameterType="com.benzhitech.model.UserDO">
        insert into fee_user (
        	id,project_id, user_name, wx_name, wx_Id,phone,wx_session,gmt_created,gmt_modified
        ) values (
        	#{id},#{projectId}, #{userName}, #{wxName}, #{wxId}, #{phone}, #{wxSession}, #{gmtCreated},#{gmtModified}
        )
    </insert>

</mapper>