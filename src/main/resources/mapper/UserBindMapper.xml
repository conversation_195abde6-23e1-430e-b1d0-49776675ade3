<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benzhitech.mapper.UserBindMapper">

    <resultMap id="userBindMap" type="com.benzhitech.model.UserBindDO">
    	<result property="id" column="id" javaType="java.lang.Long"></result>
    	<result property="userId" column="user_id" javaType="java.lang.Long"></result>
        <result property="wxId" column="wx_Id" javaType="java.lang.String"></result>        
    	<result property="projectId" column="project_id" javaType="java.lang.Long"></result>
    	
        <result property="mId" column="m_id" javaType="java.lang.String"></result>
        <result property="render" column="render" javaType="java.lang.String"></result>
		<result property="addr" column="addr" javaType="java.lang.String"></result>
		<result property="phone" column="phone" javaType="java.lang.String"></result>
		
		 <result property="bind" column="bind" javaType="java.lang.Integer"></result>
		 
        <result property="gmtCreated" column="gmt_created" javaType="java.util.Date"></result>
        <result property="gmtModified" column="gmt_modified" javaType="java.util.Date"></result>
        <result property="deleted" column="deleted" javaType="java.lang.Integer"></result>
    </resultMap>

    <select id="getUserBinds" resultMap="userBindMap" parameterType="java.lang.Long">
        select * from user_bind where user_id = #{userId} and project_id = #{projectId} and deleted=0
    </select>
    <select id="getUserBind" resultMap="userBindMap" parameterType="java.util.Map">
        select * from user_bind where user_id = #{userId} and project_id = #{projectId} and m_id =  #{mId} and deleted=0
    </select>
    <insert id="addUserBind" parameterType="com.benzhitech.model.UserBindDO" useGeneratedKeys="true" keyProperty="id">
        insert into user_bind (
        	user_id, wx_Id, project_id, m_id,render,addr,phone,bind,gmt_created,gmt_modified,deleted
        ) values (
        	#{userId}, #{wxId}, #{projectId}, #{mId}, #{render},#{addr},#{phone}, #{bind}, #{gmtCreated},#{gmtModified},0
        )
    </insert>

	<update id="updateUserBind" parameterType="com.benzhitech.model.UserBindDO">
		<![CDATA[
		UPDATE
			user_bind 
		SET
		]]>
		<if test="phone!=null">
			phone=#{phone},
		</if>
		<if test="bind!=null">
			bind=#{bind},
		</if>
		<if test="deleted!=null">
			deleted=#{deleted},
		</if>
		gmt_modified = #{gmtModified}
				
		WHERE 
			id=#{id} and project_id = #{projectId} 
			<if test="userId!=null">
				and user_id=#{userId}
			</if>
	</update>

</mapper>