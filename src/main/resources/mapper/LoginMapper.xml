<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benzhitech.mapper.LoginMapper">

    <insert id="addLogin" parameterType="com.benzhitech.model.LoginDO">
        insert into fee_login (user_id, wx_id, expire, device,gmt_created, gmt_modified) values ( #{userId}, #{wxId}, #{expire}, #{device},#{gmtCreated},#{gmtModified})
    </insert>
    
</mapper>