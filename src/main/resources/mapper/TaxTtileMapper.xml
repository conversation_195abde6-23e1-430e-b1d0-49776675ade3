<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benzhitech.mapper.TaxTitleMapper">

    <resultMap id="taxTitleDOMap" type="com.benzhitech.model.TaxTitleDO">
        <result property="id" column="id" javaType="java.lang.Long"></result>
        <result property="userId" column="user_id" javaType="java.lang.Long"></result>
        <result property="projectId" column="project_id" javaType="java.lang.Long"></result>

        <result property="taxNo" column="tax_no" javaType="java.lang.String"></result>
        <result property="taxName" column="tax_name" javaType="java.lang.String"></result>
        <result property="remark" column="remark" javaType="java.lang.String"></result>
        <result property="email" column="email" javaType="java.lang.String"></result>
    </resultMap>


    <!-- 插入一条新的税务信息 -->
    <insert id="insert">
        INSERT INTO tax_title (id, project_id, user_id, tax_no, tax_name, remark, email)
        VALUES (
                   #{id},
                   #{projectId},
                   #{userId},
                   #{taxNo},
                   #{taxName},
                   #{remark},
                   #{email}
               )
    </insert>

    <!-- 根据用户ID查询税务信息 -->
    <select id="queryByUserId" resultMap="taxTitleDOMap">
        SELECT id, project_id, user_id, tax_no, tax_name, remark, email FROM tax_title
        WHERE user_id = #{userId} and project_id = #{projectId}
    </select>

    <!-- 根据ID更新税务信息 -->
    <update id="updateById"  parameterType="com.benzhitech.model.TaxTitleDO">
        UPDATE tax_title
        SET
            tax_no = #{taxNo},
            tax_name = #{taxName},
            remark = #{remark},
            email = #{email}
        WHERE id = #{id} and  project_id = #{projectId}
    </update>
</mapper>