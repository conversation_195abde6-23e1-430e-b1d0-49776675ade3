<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.benzhitech.mapper.OrderMapper">

    <resultMap id="orderMap" type="com.benzhitech.model.OrderDO">
    	<result property="id" column="id" javaType="java.lang.Long"></result>
        <result property="userId" column="user_id" javaType="java.lang.Long"></result>
        <result property="projectId" column="project_id" javaType="java.lang.Long"></result>
        <result property="mId" column="m_id" javaType="java.lang.String"></result>
        <result property="wxId" column="wx_Id" javaType="java.lang.String"></result>
        <result property="creator" column="creator" javaType="java.lang.String"></result>
        <result property="status" column="status" javaType="java.lang.Integer"></result>
        <result property="fitStatus" column="fit_status" javaType="java.lang.Integer"></result>
        <result property="amount" column="amount" javaType="java.lang.Integer"></result>
        <result property="prepayId" column="prepay_id" javaType="java.lang.String"></result>
        <result property="wxTradeNo" column="wx_trade_no" javaType="java.lang.String"></result>
		<result property="invoiceState" column="invoice_state" javaType="java.lang.Integer"></result>
		<result property="taxType" column="tax_type" javaType="java.lang.Integer"></result>
		<result property="invoiceFile" column="invoice_file" javaType="java.lang.String"></result>
        <result property="version" column="version" javaType="java.lang.Integer"></result>
        <result property="gmtCreated" column="gmt_created" javaType="java.util.Date"></result>
        <result property="gmtModified" column="gmt_modified" javaType="java.util.Date"></result>
    </resultMap>


    <insert id="insert" parameterType="com.benzhitech.model.OrderDO">
        insert into fee_order (
	        id,
	        user_id,
	        project_id,
	        m_id,
	        wx_id,
	        creator,
	        status,
	        fit_status,
	        amount,
	        prepay_id,
	        wx_trade_no,
	        version,
	        gmt_created,
	        gmt_modified
        ) values ( 
	        #{id},
	        #{userId},
	        #{projectId},
	        #{mId},
	        #{wxId},
	        #{creator},
	        #{status},
	        #{fitStatus},
	        #{amount},
	        #{prepayId},
	        #{wxTradeNo},
	        #{version},
	        #{gmtCreated},
	        #{gmtModified})
    </insert>
    <select id="queryById" resultMap="orderMap" parameterType="java.lang.Long">
        select * from fee_order where id = #{id} and project_id = #{projectId}
    </select>
    <select id="queryList" resultMap="orderMap" parameterType="java.util.Map">
        select * from fee_order where m_id = #{mId} and project_id= #{projectId} and status >=1 order by gmt_created desc
    </select>
    <update id="update" parameterType="com.benzhitech.model.OrderDO">
		<![CDATA[
		UPDATE
			fee_order 
		SET
			gmt_modified = #{gmtModified},
		]]>
		<if test="status!=null">
			status=#{status},
		</if>
		<if test="fitStatus!=null">
			fit_status=#{fitStatus},
		</if>
		<if test="wxTradeNo!=null">
			wx_trade_no=#{wxTradeNo},
		</if>
		<if test="invoiceState!=null">
			invoice_state= #{invoiceState},
		</if>
		<if test="taxType!=null">
			tax_type=#{taxType},
		</if>
		<if test="invoiceFile!=null">
			invoice_file=#{invoiceFile},
		</if>
		version = version+1
		WHERE 
			id=#{id} and project_id = #{projectId} 
			<if test="userId!=null">
				and user_id=#{userId}
			</if>
			<if test="version!=null">
				and version=#{version}
			</if>
	</update>
	
</mapper>