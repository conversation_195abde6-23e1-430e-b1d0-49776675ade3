server:
   port: 8181
   #port: 443
   #ssl:
   #   key-store: classpath:app.benzhitech.com.pfx
   #   key-store-password: w5ylr0id #QS5ojfx8
   #   keyStoreType: PKCS12
  
spring.freemarker:
    allow-request-override: false
    cache: false
    check-template-location: true
    charset: utf-8
    content-type: text/html
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .ftl
    template-loader-path: classpath:/templates/
    

# SqlServer config
spring.datasource:
  driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver

  #test 47.99.180.121
  #url: **************************************************************
  #test account
  #username: feemaster
  #password: hZfxlD!23

  #pro 8.136.147.44
  #local pro
  #url: ******************************************************
  #remote pro
  #url: *********************************************************
  #pro account
  #username: feemaster
  #password: P2&xZj!2

  #kuming pro
  username: feemaster
  password: 5y!4x50.1
  #kuming remote
  #url: ***********************************************************
  #kuming local
  url: ******************************************************

mybatis:
  type-aliases-package: com.benzhitech.mapper.*
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath:mapper/*.xml

logging:
  level:
    root: info
  