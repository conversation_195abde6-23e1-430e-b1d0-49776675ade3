<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
       <groupId>org.springframework.boot</groupId>
       <artifactId>spring-boot-starter-parent</artifactId>
       <version>2.0.5.RELEASE</version>
   </parent>

  <groupId>com.benzhitech</groupId>
  <artifactId>feeservice</artifactId>
  <version>1.8.35-km</version>
  <packaging>jar</packaging>

  <name>feeservice</name>
  <url>http://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>GBK</project.build.sourceEncoding>
  </properties>

  <dependencies>

	<!-- Springboot核心jar包 -->
	<dependency>
	      <groupId>org.springframework.boot</groupId>
	      <artifactId>spring-boot-starter</artifactId>
	  </dependency>
	  <!-- web开发包：包含Tomcat和Springmvc -->
	<dependency>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-web</artifactId>
	</dependency>

	<!-- spring-boot热部署 -->
	<dependency>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-devtools</artifactId>
	</dependency>
	
	<dependency>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-freemarker</artifactId>
	</dependency>
	<dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-logging</artifactId>
	</dependency>
	<dependency>
	    <groupId>org.mybatis.spring.boot</groupId>
	    <artifactId>mybatis-spring-boot-starter</artifactId>
	    <version>2.1.0</version>
	</dependency>
	<dependency>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-configuration-processor</artifactId>
		<optional>true</optional>
	</dependency>

	<dependency>
		<groupId>com.microsoft.sqlserver</groupId>
		<artifactId>sqljdbc4</artifactId>
		<version>4.0</version>
	</dependency>
	<dependency>
	    <groupId>com.microsoft.sqlserver</groupId>
	    <artifactId>mssql-jdbc</artifactId>
	    <version>6.2.0.jre8</version>
	    <scope>runtime</scope>
	</dependency>	
	<dependency>
	    <groupId>org.projectlombok</groupId>
	    <artifactId>lombok</artifactId>
	</dependency>
	<dependency>
	    <groupId>com.alibaba</groupId>
	    <artifactId>fastjson</artifactId>
	    <version>1.2.46</version>
	</dependency>
	
	<dependency>
	    <groupId>org.apache.httpcomponents</groupId>
	    <artifactId>httpclient</artifactId>
		<version>4.5.13</version>
	</dependency>
     <dependency>
         <groupId>commons-httpclient</groupId>
         <artifactId>commons-httpclient</artifactId>
         <version>3.0.1</version>
     </dependency>
	<dependency>
	    <groupId>dom4j</groupId>
	    <artifactId>dom4j</artifactId>
	    <version>1.6</version>
	</dependency>
	<dependency>
	    <groupId>org.jdom</groupId>
	    <artifactId>jdom</artifactId>
	    <version>1.1.3</version>
	</dependency>
	<dependency>
	    <groupId>org.yaml</groupId>
	    <artifactId>snakeyaml</artifactId>
	    <version>1.29</version>
	</dependency>
	  <!-- 以下通联支付引入 -->
	<dependency>
		<groupId>org.bouncycastle</groupId>
		<artifactId>bcprov-jdk15on</artifactId>
		<version>1.59</version>
	</dependency>
	  <dependency>
		  <groupId>net.sf.json-lib</groupId>
		  <artifactId>json-lib</artifactId>
		  <version>2.4</version>
		  <classifier>jdk15</classifier>
	  </dependency>
	  <!-- 通联支付结束 -->

	  <!-- 以下阿里云短信SDK -->
	  <dependency>
		  <groupId>com.aliyun</groupId>
		  <artifactId>dysmsapi20170525</artifactId>
		  <version>3.1.0</version>
	  </dependency>
	  <!-- 阿里云短信SDK结束 -->

	  <!-- 以下Liusimao短信SDK -->
	  <!-- Jersey Client -->
	  <dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-client</artifactId>
		  <version>1.19.4</version>
	  </dependency>

	  <!-- Jersey Core -->
	  <dependency>
		  <groupId>com.sun.jersey</groupId>
		  <artifactId>jersey-core</artifactId>
		  <version>1.19.4</version>
	  </dependency>

	  <!-- JSON -->
	  <dependency>
		  <groupId>org.json</groupId>
		  <artifactId>json</artifactId>
		  <version>20210307</version>
	  </dependency>
	  <!-- LiusimaoSDK结束 -->

  </dependencies>
  
  
  <build>
	<plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <jvmArguments>
            -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5005
          </jvmArguments>
        </configuration>
      </plugin>	
	</plugins>
	<resources>
	    <resource>
	        <directory>src/main/java</directory>
	        <includes>
	            <include>**/*.yml</include>
	            <include>**/*.properties</include>
	            <include>**/*.xml</include>
                <include>**/*.pfx</include>
                <include>**/*.ftl</include>
	        </includes>
	        <filtering>false</filtering>
	    </resource>
	    <resource>
	        <directory>src/main/resources</directory>
	        <includes>
	            <include>**/*.yml</include>
	            <include>**/*.properties</include>
	            <include>**/*.xml</include>
                <include>**/*.pfx</include>
                <include>**/*.ftl</include>
	        </includes>
	        <filtering>false</filtering>
	    </resource>
	</resources>
  </build>

</project>
